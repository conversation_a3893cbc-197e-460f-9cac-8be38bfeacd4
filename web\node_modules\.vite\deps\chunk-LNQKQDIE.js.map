{"version": 3, "sources": ["../../.pnpm/svelte@5.38.0/node_modules/svelte/src/version.js", "../../.pnpm/svelte@5.38.0/node_modules/svelte/src/internal/disclose-version.js"], "sourcesContent": ["// generated during release, do not modify\n\n/**\n * The current version, as set in package.json.\n * @type {string}\n */\nexport const VERSION = '5.38.0';\nexport const PUBLIC_VERSION = '5';\n", "import { PUBLIC_VERSION } from '../version.js';\n\nif (typeof window !== 'undefined') {\n\t// @ts-expect-error\n\t((window.__svelte ??= {}).v ??= new Set()).add(PUBLIC_VERSION);\n}\n"], "mappings": ";AAOO,IAAM,iBAAiB;;;ACL9B,IAAI,OAAO,WAAW,aAAa;AAElC,IAAE,OAAO,aAAa,CAAC,GAAG,MAAM,oBAAI,IAAI,GAAG,IAAI,cAAc;AAC9D;", "names": []}