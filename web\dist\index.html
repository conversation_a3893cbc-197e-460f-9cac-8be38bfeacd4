<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vite + Svelte + TS</title>
    <!-- 引入 Chart.js UMD 版本，便于简单使用 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.6/dist/chart.umd.min.js"></script>
    <script type="module" crossorigin src="/assets/index-B95rvfaH.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-ReWTuU5w.css">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
