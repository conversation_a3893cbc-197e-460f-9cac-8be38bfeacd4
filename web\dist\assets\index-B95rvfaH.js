(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))r(a);new MutationObserver(a=>{for(const s of a)if(s.type==="childList")for(const i of s.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(a){const s={};return a.integrity&&(s.integrity=a.integrity),a.referrerPolicy&&(s.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?s.credentials="include":a.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(a){if(a.ep)return;a.ep=!0;const s=n(a);fetch(a.href,s)}})();const Lr=!1;var tn=Array.isArray,Fs=Array.prototype.indexOf,Kn=Array.from,Gt=Object.defineProperty,Fe=Object.getOwnPropertyDescriptor,Fr=Object.getOwnPropertyDescriptors,qs=Object.prototype,js=Array.prototype,Wn=Object.getPrototypeOf,_r=Object.isExtensible;function lt(e){return typeof e=="function"}const ae=()=>{};function Us(e){return typeof e?.then=="function"}function Bs(e){return e()}function At(e){for(var t=0;t<e.length;t++)e[t]()}function Hs(){var e,t,n=new Promise((r,a)=>{e=r,t=a});return{promise:n,resolve:e,reject:t}}function Vs(e,t){if(Array.isArray(e))return e;if(!(Symbol.iterator in e))return Array.from(e);const n=[];for(const r of e)if(n.push(r),n.length===t)break;return n}const ie=2,Gn=4,nn=8,bt=16,Ne=32,et=64,qr=128,ce=256,Xt=512,Q=1024,fe=2048,Oe=4096,ge=8192,tt=16384,rn=32768,Rt=65536,mr=1<<17,Ys=1<<18,Xn=1<<19,Jn=1<<20,Nn=1<<21,Zn=1<<22,We=1<<23,qe=Symbol("$state"),jr=Symbol("legacy props"),Ks=Symbol(""),Qn=new class extends Error{name="StaleReactionError";message="The reaction that called `getAbortSignal()` was re-run or destroyed"};function Ws(){throw new Error("https://svelte.dev/e/await_outside_boundary")}function sn(e){throw new Error("https://svelte.dev/e/lifecycle_outside_component")}function Gs(){throw new Error("https://svelte.dev/e/async_derived_orphan")}function Xs(e){throw new Error("https://svelte.dev/e/effect_in_teardown")}function Js(){throw new Error("https://svelte.dev/e/effect_in_unowned_derived")}function Zs(e){throw new Error("https://svelte.dev/e/effect_orphan")}function Qs(){throw new Error("https://svelte.dev/e/effect_update_depth_exceeded")}function ea(e){throw new Error("https://svelte.dev/e/props_invalid_value")}function ta(){throw new Error("https://svelte.dev/e/state_descriptors_fixed")}function na(){throw new Error("https://svelte.dev/e/state_prototype_fixed")}function ra(){throw new Error("https://svelte.dev/e/state_unsafe_mutation")}const an=1,ln=2,Ur=4,sa=8,aa=16,ia=1,la=2,Br=4,oa=8,ua=16,ca=1,fa=2,va=4,da=1,pa=2,K=Symbol(),ha="http://www.w3.org/1999/xhtml";let _a=!1;function Hr(e){return e===this.v}function er(e,t){return e!=e?t==t:e!==t||e!==null&&typeof e=="object"||typeof e=="function"}function ma(e,t){return e!==t}function Vr(e){return!er(e,this.v)}let yt=!1,ga=!1;function ba(){yt=!0}let j=null;function ht(e){j=e}function On(e){return Yr().get(e)}function gn(e,t){return Yr().set(e,t),t}function ke(e,t=!1,n){j={p:j,c:null,e:null,s:e,x:null,l:yt&&!t?{s:null,u:null,$:[]}:null}}function Ae(e){var t=j,n=t.e;if(n!==null){t.e=null;for(var r of n)is(r)}return j=t.p,{}}function Ve(){return!yt||j!==null&&j.l===null}function Yr(e){return j===null&&sn(),j.c??=new Map(ya(j)||void 0)}function ya(e){let t=e.p;for(;t!==null;){const n=t.c;if(n!==null)return n;t=t.p}return null}const wa=new WeakMap;function xa(e){var t=C;if(t===null)return $.f|=We,e;if((t.f&rn)===0){if((t.f&qr)===0)throw!t.parent&&e instanceof Error&&Kr(e),e;t.b.error(e)}else tr(e,t)}function tr(e,t){for(;t!==null;){if((t.f&qr)!==0)try{t.b.error(e);return}catch(n){e=n}t=t.parent}throw e instanceof Error&&Kr(e),e}function Kr(e){const t=wa.get(e);t&&(Gt(e,"message",{value:t.message}),Gt(e,"stack",{value:t.stack}))}let Tt=[],In=[];function Wr(){var e=Tt;Tt=[],At(e)}function Sa(){var e=In;In=[],At(e)}function Nt(e){Tt.length===0&&queueMicrotask(Wr),Tt.push(e)}function Ea(){Tt.length>0&&Wr(),In.length>0&&Sa()}function ka(){for(var e=C.b;e!==null&&!e.has_pending_snippet();)e=e.parent;return e===null&&Ws(),e}function Ot(e){var t=ie|fe,n=$!==null&&($.f&ie)!==0?$:null;return C===null||n!==null&&(n.f&ce)!==0?t|=ce:C.f|=Xn,{ctx:j,deps:null,effects:null,equals:Hr,f:t,fn:e,reactions:null,rv:0,v:K,wv:0,parent:n??C,ac:null}}function Aa(e,t){let n=C;n===null&&Gs();var r=n.b,a=void 0,s=Be(K),i=null,l=!$;return La(()=>{try{var o=e()}catch(p){o=Promise.reject(p)}var u=()=>o;a=i?.then(u,u)??Promise.resolve(o),i=a;var f=q,_=r.pending;l&&(r.update_pending_count(1),_||f.increment());const v=(p,d=void 0)=>{i=null,_||f.activate(),d?d!==Qn&&(s.f|=We,je(s,d)):((s.f&We)!==0&&(s.f^=We),je(s,p)),l&&(r.update_pending_count(-1),_||f.decrement()),Jr()};if(a.then(v,p=>v(null,p||"unknown")),f)return()=>{queueMicrotask(()=>f.neuter())}}),new Promise(o=>{function u(f){function _(){f===a?o(s):u(a)}f.then(_,_)}u(a)})}function Ta(e){const t=Ot(e);return vs(t),t}function nr(e){const t=Ot(e);return t.equals=Vr,t}function Gr(e){var t=e.effects;if(t!==null){e.effects=null;for(var n=0;n<t.length;n+=1)Re(t[n])}}function za(e){for(var t=e.parent;t!==null;){if((t.f&ie)===0)return t;t=t.parent}return null}function rr(e){var t,n=C;de(za(e));try{Gr(e),t=_s(e)}finally{de(n)}return t}function Xr(e){var t=rr(e);if(e.equals(t)||(e.v=t,e.wv=ps()),!rt)if(_t!==null)_t.set(e,e.v);else{var n=(Le||(e.f&ce)!==0)&&e.deps!==null?Oe:Q;ne(e,n)}}function Pa(e,t,n){const r=Ve()?Ot:nr;if(t.length===0){n(e.map(r));return}var a=q,s=C,i=Ra(),l=ka();Promise.all(t.map(o=>Aa(o))).then(o=>{a?.activate(),i();try{n([...e.map(r),...o])}catch(u){(s.f&tt)===0&&tr(u,s)}a?.deactivate(),Jr()}).catch(o=>{l.error(o)})}function Ra(){var e=C,t=$,n=j;return function(){de(e),ve(t),ht(n)}}function Jr(){de(null),ve(null),ht(null)}const Et=new Set;let q=null,Yt=null,_t=null,gr=new Set,Jt=[];function Zr(){const e=Jt.shift();Jt.length>0&&queueMicrotask(Zr),e()}let Je=[],on=null,Cn=!1,Kt=!1;class mt{current=new Map;#s=new Map;#a=new Set;#e=0;#u=null;#c=!1;#n=[];#i=[];#r=[];#t=[];#l=[];#f=[];#v=[];skipped_effects=new Set;process(t){Je=[],Yt=null;var n=null;if(Et.size>1){n=new Map,_t=new Map;for(const[s,i]of this.current)n.set(s,{v:s.v,wv:s.wv}),s.v=i;for(const s of Et)if(s!==this)for(const[i,l]of s.#s)n.has(i)||(n.set(i,{v:i.v,wv:i.wv}),i.v=l)}for(const s of t)this.#p(s);if(this.#n.length===0&&this.#e===0){this.#d();var r=this.#r,a=this.#t;this.#r=[],this.#t=[],this.#l=[],Yt=q,q=null,br(r),br(a),q===null?q=this:Et.delete(this),this.#u?.resolve()}else this.#o(this.#r),this.#o(this.#t),this.#o(this.#l);if(n){for(const[s,{v:i,wv:l}]of n)s.wv<=l&&(s.v=i);_t=null}for(const s of this.#n)pt(s);for(const s of this.#i)pt(s);this.#n=[],this.#i=[]}#p(t){t.f^=Q;for(var n=t.first;n!==null;){var r=n.f,a=(r&(Ne|et))!==0,s=a&&(r&Q)!==0,i=s||(r&ge)!==0||this.skipped_effects.has(n);if(!i&&n.fn!==null){if(a)n.f^=Q;else if((r&Q)===0)if((r&Gn)!==0)this.#t.push(n);else if((r&Zn)!==0){var l=n.b?.pending?this.#i:this.#n;l.push(n)}else Mt(n)&&((n.f&bt)!==0&&this.#l.push(n),pt(n));var o=n.first;if(o!==null){n=o;continue}}var u=n.parent;for(n=n.next;n===null&&u!==null;)n=u.next,u=u.parent}}#o(t){for(const n of t)((n.f&fe)!==0?this.#f:this.#v).push(n),ne(n,Q);t.length=0}capture(t,n){this.#s.has(t)||this.#s.set(t,n),this.current.set(t,t.v)}activate(){q=this}deactivate(){q=null,Yt=null;for(const t of gr)if(gr.delete(t),t(),q!==null)break}neuter(){this.#c=!0}flush(){Je.length>0?Qr():this.#d(),q===this&&(this.#e===0&&Et.delete(this),this.deactivate())}#d(){if(!this.#c)for(const t of this.#a)t();this.#a.clear()}increment(){this.#e+=1}decrement(){if(this.#e-=1,this.#e===0){for(const t of this.#f)ne(t,fe),Ze(t);for(const t of this.#v)ne(t,Oe),Ze(t);this.#r=[],this.#t=[],this.flush()}else this.deactivate()}add_callback(t){this.#a.add(t)}settled(){return(this.#u??=Hs()).promise}static ensure(){if(q===null){const t=q=new mt;Et.add(q),Kt||mt.enqueue(()=>{q===t&&t.flush()})}return q}static enqueue(t){Jt.length===0&&queueMicrotask(Zr),Jt.unshift(t)}}function Na(e){var t=Kt;Kt=!0;try{for(var n;;){if(Ea(),Je.length===0&&(q?.flush(),Je.length===0))return on=null,n;Qr()}}finally{Kt=t}}function Qr(){var e=dt;Cn=!0;try{var t=0;for(xr(!0);Je.length>0;){var n=mt.ensure();if(t++>1e3){var r,a;Oa()}n.process(Je),Ge.clear()}}finally{Cn=!1,xr(e),on=null}}function Oa(){try{Qs()}catch(e){tr(e,on)}}function br(e){var t=e.length;if(t!==0){for(var n=0;n<t;){var r=e[n++];if((r.f&(tt|ge))===0&&Mt(r)){var a=q?q.current.size:0;if(pt(r),r.deps===null&&r.first===null&&r.nodes_start===null&&(r.teardown===null&&r.ac===null?us(r):r.fn=null),q!==null&&q.current.size>a&&(r.f&Jn)!==0)break}}for(;n<t;)Ze(e[n++])}}function Ze(e){for(var t=on=e;t.parent!==null;){t=t.parent;var n=t.f;if(Cn&&t===C&&(n&bt)!==0)return;if((n&(et|Ne))!==0){if((n&Q)===0)return;t.f^=Q}}Je.push(t)}const Ge=new Map;function Be(e,t){var n={f:0,v:e,reactions:null,equals:Hr,rv:0,wv:0};return n}function Me(e,t){const n=Be(e);return vs(n),n}function B(e,t=!1,n=!0){const r=Be(e);return t||(r.equals=Vr),yt&&n&&j!==null&&j.l!==null&&(j.l.s??=[]).push(r),r}function G(e,t){return M(e,H(()=>c(e))),t}function M(e,t,n=!1){$!==null&&(!xe||($.f&mr)!==0)&&Ve()&&($.f&(ie|bt|Zn|mr))!==0&&!Pe?.includes(e)&&ra();let r=n?ot(t):t;return je(e,r)}function je(e,t){if(!e.equals(t)){var n=e.v;rt?Ge.set(e,t):Ge.set(e,n),e.v=t;var r=mt.ensure();r.capture(e,n),(e.f&ie)!==0&&((e.f&fe)!==0&&rr(e),ne(e,(e.f&ce)===0?Q:Oe)),e.wv=ps(),es(e,fe),Ve()&&C!==null&&(C.f&Q)!==0&&(C.f&(Ne|et))===0&&(le===null?ja([e]):le.push(e))}return t}function yr(e,t=1){var n=c(e),r=t===1?n++:n--;return M(e,n),r}function bn(e){M(e,e.v+1)}function es(e,t){var n=e.reactions;if(n!==null)for(var r=Ve(),a=n.length,s=0;s<a;s++){var i=n[s],l=i.f;if(!(!r&&i===C)){var o=(l&fe)===0;o&&ne(i,t),(l&ie)!==0?es(i,Oe):o&&Ze(i)}}}function ot(e){if(typeof e!="object"||e===null||qe in e)return e;const t=Wn(e);if(t!==qs&&t!==js)return e;var n=new Map,r=tn(e),a=Me(0),s=Xe,i=l=>{if(Xe===s)return l();var o=$,u=Xe;ve(null),Er(s);var f=l();return ve(o),Er(u),f};return r&&n.set("length",Me(e.length)),new Proxy(e,{defineProperty(l,o,u){(!("value"in u)||u.configurable===!1||u.enumerable===!1||u.writable===!1)&&ta();var f=n.get(o);return f===void 0?f=i(()=>{var _=Me(u.value);return n.set(o,_),_}):M(f,u.value,!0),!0},deleteProperty(l,o){var u=n.get(o);if(u===void 0){if(o in l){const f=i(()=>Me(K));n.set(o,f),bn(a)}}else M(u,K),bn(a);return!0},get(l,o,u){if(o===qe)return e;var f=n.get(o),_=o in l;if(f===void 0&&(!_||Fe(l,o)?.writable)&&(f=i(()=>{var p=ot(_?l[o]:K),d=Me(p);return d}),n.set(o,f)),f!==void 0){var v=c(f);return v===K?void 0:v}return Reflect.get(l,o,u)},getOwnPropertyDescriptor(l,o){var u=Reflect.getOwnPropertyDescriptor(l,o);if(u&&"value"in u){var f=n.get(o);f&&(u.value=c(f))}else if(u===void 0){var _=n.get(o),v=_?.v;if(_!==void 0&&v!==K)return{enumerable:!0,configurable:!0,value:v,writable:!0}}return u},has(l,o){if(o===qe)return!0;var u=n.get(o),f=u!==void 0&&u.v!==K||Reflect.has(l,o);if(u!==void 0||C!==null&&(!f||Fe(l,o)?.writable)){u===void 0&&(u=i(()=>{var v=f?ot(l[o]):K,p=Me(v);return p}),n.set(o,u));var _=c(u);if(_===K)return!1}return f},set(l,o,u,f){var _=n.get(o),v=o in l;if(r&&o==="length")for(var p=u;p<_.v;p+=1){var d=n.get(p+"");d!==void 0?M(d,K):p in l&&(d=i(()=>Me(K)),n.set(p+"",d))}if(_===void 0)(!v||Fe(l,o)?.writable)&&(_=i(()=>Me(void 0)),M(_,ot(u)),n.set(o,_));else{v=_.v!==K;var h=i(()=>ot(u));M(_,h)}var m=Reflect.getOwnPropertyDescriptor(l,o);if(m?.set&&m.set.call(f,u),!v){if(r&&typeof o=="string"){var g=n.get("length"),b=Number(o);Number.isInteger(b)&&b>=g.v&&M(g,b+1)}bn(a)}return!0},ownKeys(l){c(a);var o=Reflect.ownKeys(l).filter(_=>{var v=n.get(_);return v===void 0||v.v!==K});for(var[u,f]of n)f.v!==K&&!(u in l)&&o.push(u);return o},setPrototypeOf(){na()}})}var wr,ts,ns,rs;function Ia(){if(wr===void 0){wr=window,ts=/Firefox/.test(navigator.userAgent);var e=Element.prototype,t=Node.prototype,n=Text.prototype;ns=Fe(t,"firstChild").get,rs=Fe(t,"nextSibling").get,_r(e)&&(e.__click=void 0,e.__className=void 0,e.__attributes=null,e.__style=void 0,e.__e=void 0),_r(n)&&(n.__t=void 0)}}function nt(e=""){return document.createTextNode(e)}function Zt(e){return ns.call(e)}function un(e){return rs.call(e)}function x(e,t){return Zt(e)}function me(e,t){{var n=Zt(e);return n instanceof Comment&&n.data===""?un(n):n}}function S(e,t=1,n=!1){let r=e;for(;t--;)r=un(r);return r}function Ca(e){e.textContent=""}function cn(){return!1}function ss(e){C===null&&$===null&&Zs(),$!==null&&($.f&ce)!==0&&C===null&&Js(),rt&&Xs()}function Ma(e,t){var n=t.last;n===null?t.last=t.first=e:(n.next=e,e.prev=n,t.last=e)}function Te(e,t,n,r=!0){var a=C;a!==null&&(a.f&ge)!==0&&(e|=ge);var s={ctx:j,deps:null,nodes_start:null,nodes_end:null,f:e|fe,first:null,fn:t,last:null,next:null,parent:a,b:a&&a.b,prev:null,teardown:null,transitions:null,wv:0,ac:null};if(n)try{pt(s),s.f|=rn}catch(o){throw Re(s),o}else t!==null&&Ze(s);var i=n&&s.deps===null&&s.first===null&&s.nodes_start===null&&s.teardown===null&&(s.f&Xn)===0;if(!i&&r&&(a!==null&&Ma(s,a),$!==null&&($.f&ie)!==0&&(e&et)===0)){var l=$;(l.effects??=[]).push(s)}return s}function as(e){const t=Te(nn,null,!1);return ne(t,Q),t.teardown=e,t}function Mn(e){ss();var t=C.f,n=!$&&(t&Ne)!==0&&(t&rn)===0;if(n){var r=j;(r.e??=[]).push(e)}else return is(e)}function is(e){return Te(Gn|Jn,e,!1)}function Da(e){return ss(),Te(nn|Jn,e,!0)}function $a(e){mt.ensure();const t=Te(et,e,!0);return(n={})=>new Promise(r=>{n.outro?Ue(t,()=>{Re(t),r(void 0)}):(Re(t),r(void 0))})}function sr(e){return Te(Gn,e,!1)}function Qe(e,t){var n=j,r={effect:null,ran:!1,deps:e};n.l.$.push(r),r.effect=fn(()=>{e(),!r.ran&&(r.ran=!0,H(t))})}function It(){var e=j;fn(()=>{for(var t of e.l.$){t.deps();var n=t.effect;(n.f&Q)!==0&&ne(n,Oe),Mt(n)&&pt(n),t.ran=!1}})}function La(e){return Te(Zn|Xn,e,!0)}function fn(e,t=0){return Te(nn|t,e,!0)}function oe(e,t=[],n=[]){Pa(t,n,r=>{Te(nn,()=>e(...r.map(c)),!0)})}function Ct(e,t=0){var n=Te(bt|t,e,!0);return n}function He(e,t=!0){return Te(Ne,e,!0,t)}function ls(e){var t=e.teardown;if(t!==null){const n=rt,r=$;Sr(!0),ve(null);try{t.call(null)}finally{Sr(n),ve(r)}}}function os(e,t=!1){var n=e.first;for(e.first=e.last=null;n!==null;){n.ac?.abort(Qn);var r=n.next;(n.f&et)!==0?n.parent=null:Re(n,t),n=r}}function Fa(e){for(var t=e.first;t!==null;){var n=t.next;(t.f&Ne)===0&&Re(t),t=n}}function Re(e,t=!0){var n=!1;(t||(e.f&Ys)!==0)&&e.nodes_start!==null&&e.nodes_end!==null&&(qa(e.nodes_start,e.nodes_end),n=!0),os(e,t&&!n),Qt(e,0),ne(e,tt);var r=e.transitions;if(r!==null)for(const s of r)s.stop();ls(e);var a=e.parent;a!==null&&a.first!==null&&us(e),e.next=e.prev=e.teardown=e.ctx=e.deps=e.fn=e.nodes_start=e.nodes_end=e.ac=null}function qa(e,t){for(;e!==null;){var n=e===t?null:un(e);e.remove(),e=n}}function us(e){var t=e.parent,n=e.prev,r=e.next;n!==null&&(n.next=r),r!==null&&(r.prev=n),t!==null&&(t.first===e&&(t.first=r),t.last===e&&(t.last=n))}function Ue(e,t){var n=[];ar(e,n,!0),cs(n,()=>{Re(e),t&&t()})}function cs(e,t){var n=e.length;if(n>0){var r=()=>--n||t();for(var a of e)a.out(r)}else t()}function ar(e,t,n){if((e.f&ge)===0){if(e.f^=ge,e.transitions!==null)for(const i of e.transitions)(i.is_global||n)&&t.push(i);for(var r=e.first;r!==null;){var a=r.next,s=(r.f&Rt)!==0||(r.f&Ne)!==0;ar(r,t,s?n:!1),r=a}}}function vn(e){fs(e,!0)}function fs(e,t){if((e.f&ge)!==0){e.f^=ge,(e.f&Q)===0&&(ne(e,fe),Ze(e));for(var n=e.first;n!==null;){var r=n.next,a=(n.f&Rt)!==0||(n.f&Ne)!==0;fs(n,a?t:!1),n=r}if(e.transitions!==null)for(const s of e.transitions)(s.is_global||t)&&s.in()}}let dt=!1;function xr(e){dt=e}let rt=!1;function Sr(e){rt=e}let $=null,xe=!1;function ve(e){$=e}let C=null;function de(e){C=e}let Pe=null;function vs(e){$!==null&&(Pe===null?Pe=[e]:Pe.push(e))}let te=null,se=0,le=null;function ja(e){le=e}let ds=1,zt=0,Xe=zt;function Er(e){Xe=e}let Le=!1;function ps(){return++ds}function Mt(e){var t=e.f;if((t&fe)!==0)return!0;if((t&Oe)!==0){var n=e.deps,r=(t&ce)!==0;if(n!==null){var a,s,i=(t&Xt)!==0,l=r&&C!==null&&!Le,o=n.length;if((i||l)&&(C===null||(C.f&tt)===0)){var u=e,f=u.parent;for(a=0;a<o;a++)s=n[a],(i||!s?.reactions?.includes(u))&&(s.reactions??=[]).push(u);i&&(u.f^=Xt),l&&f!==null&&(f.f&ce)===0&&(u.f^=ce)}for(a=0;a<o;a++)if(s=n[a],Mt(s)&&Xr(s),s.wv>e.wv)return!0}(!r||C!==null&&!Le)&&ne(e,Q)}return!1}function hs(e,t,n=!0){var r=e.reactions;if(r!==null&&!Pe?.includes(e))for(var a=0;a<r.length;a++){var s=r[a];(s.f&ie)!==0?hs(s,t,!1):t===s&&(n?ne(s,fe):(s.f&Q)!==0&&ne(s,Oe),Ze(s))}}function _s(e){var t=te,n=se,r=le,a=$,s=Le,i=Pe,l=j,o=xe,u=Xe,f=e.f;te=null,se=0,le=null,Le=(f&ce)!==0&&(xe||!dt||$===null),$=(f&(Ne|et))===0?e:null,Pe=null,ht(e.ctx),xe=!1,Xe=++zt,e.ac!==null&&(e.ac.abort(Qn),e.ac=null);try{e.f|=Nn;var _=(0,e.fn)(),v=e.deps;if(te!==null){var p;if(Qt(e,se),v!==null&&se>0)for(v.length=se+te.length,p=0;p<te.length;p++)v[se+p]=te[p];else e.deps=v=te;if(!Le||(f&ie)!==0&&e.reactions!==null)for(p=se;p<v.length;p++)(v[p].reactions??=[]).push(e)}else v!==null&&se<v.length&&(Qt(e,se),v.length=se);if(Ve()&&le!==null&&!xe&&v!==null&&(e.f&(ie|Oe|fe))===0)for(p=0;p<le.length;p++)hs(le[p],e);return a!==null&&a!==e&&(zt++,le!==null&&(r===null?r=le:r.push(...le))),(e.f&We)!==0&&(e.f^=We),_}catch(d){return xa(d)}finally{e.f^=Nn,te=t,se=n,le=r,$=a,Le=s,Pe=i,ht(l),xe=o,Xe=u}}function Ua(e,t){let n=t.reactions;if(n!==null){var r=Fs.call(n,e);if(r!==-1){var a=n.length-1;a===0?n=t.reactions=null:(n[r]=n[a],n.pop())}}n===null&&(t.f&ie)!==0&&(te===null||!te.includes(t))&&(ne(t,Oe),(t.f&(ce|Xt))===0&&(t.f^=Xt),Gr(t),Qt(t,0))}function Qt(e,t){var n=e.deps;if(n!==null)for(var r=t;r<n.length;r++)Ua(e,n[r])}function pt(e){var t=e.f;if((t&tt)===0){ne(e,Q);var n=C,r=dt;C=e,dt=!0;try{(t&bt)!==0?Fa(e):os(e),ls(e);var a=_s(e);e.teardown=typeof a=="function"?a:null,e.wv=ds;var s;Lr&&ga&&(e.f&fe)!==0&&e.deps}finally{dt=r,C=n}}}function c(e){var t=e.f,n=(t&ie)!==0;if($!==null&&!xe){var r=C!==null&&(C.f&tt)!==0;if(!r&&!Pe?.includes(e)){var a=$.deps;if(($.f&Nn)!==0)e.rv<zt&&(e.rv=zt,te===null&&a!==null&&a[se]===e?se++:te===null?te=[e]:(!Le||!te.includes(e))&&te.push(e));else{($.deps??=[]).push(e);var s=e.reactions;s===null?e.reactions=[$]:s.includes($)||s.push($)}}}else if(n&&e.deps===null&&e.effects===null){var i=e,l=i.parent;l!==null&&(l.f&ce)===0&&(i.f^=ce)}if(rt){if(Ge.has(e))return Ge.get(e);if(n){i=e;var o=i.v;return((i.f&Q)===0&&i.reactions!==null||ms(i))&&(o=rr(i)),Ge.set(i,o),o}}else if(n){if(i=e,_t?.has(i))return _t.get(i);Mt(i)&&Xr(i)}if((e.f&We)!==0)throw e.v;return e.v}function ms(e){if(e.v===K)return!0;if(e.deps===null)return!1;for(const t of e.deps)if(Ge.has(t)||(t.f&ie)!==0&&ms(t))return!0;return!1}function H(e){var t=xe;try{return xe=!0,e()}finally{xe=t}}const Ba=-7169;function ne(e,t){e.f=e.f&Ba|t}function Pt(e){if(!(typeof e!="object"||!e||e instanceof EventTarget)){if(qe in e)Dn(e);else if(!Array.isArray(e))for(let t in e){const n=e[t];typeof n=="object"&&n&&qe in n&&Dn(n)}}}function Dn(e,t=new Set){if(typeof e=="object"&&e!==null&&!(e instanceof EventTarget)&&!t.has(e)){t.add(e),e instanceof Date&&e.getTime();for(let r in e)try{Dn(e[r],t)}catch{}const n=Wn(e);if(n!==Object.prototype&&n!==Array.prototype&&n!==Map.prototype&&n!==Set.prototype&&n!==Date.prototype){const r=Fr(n);for(let a in r){const s=r[a].get;if(s)try{s.call(e)}catch{}}}}}const Ha=["touchstart","touchmove"];function Va(e){return Ha.includes(e)}let kr=!1;function Ya(){kr||(kr=!0,document.addEventListener("reset",e=>{Promise.resolve().then(()=>{if(!e.defaultPrevented)for(const t of e.target.elements)t.__on_r?.()})},{capture:!0}))}function dn(e){var t=$,n=C;ve(null),de(null);try{return e()}finally{ve(t),de(n)}}function Ka(e,t,n,r=n){e.addEventListener(t,()=>dn(n));const a=e.__on_r;a?e.__on_r=()=>{a(),r(!0)}:e.__on_r=()=>r(!0),Ya()}const Wa=new Set,Ar=new Set;function Ga(e,t,n,r={}){function a(s){if(r.capture||kt.call(t,s),!s.cancelBubble)return dn(()=>n?.call(this,s))}return e.startsWith("pointer")||e.startsWith("touch")||e==="wheel"?Nt(()=>{t.addEventListener(e,a,r)}):t.addEventListener(e,a,r),a}function W(e,t,n,r,a){var s={capture:r,passive:a},i=Ga(e,t,n,s);(t===document.body||t===window||t===document||t instanceof HTMLMediaElement)&&as(()=>{t.removeEventListener(e,i,s)})}let Tr=null;function kt(e){var t=this,n=t.ownerDocument,r=e.type,a=e.composedPath?.()||[],s=a[0]||e.target;Tr=e;var i=0,l=Tr===e&&e.__root;if(l){var o=a.indexOf(l);if(o!==-1&&(t===document||t===window)){e.__root=t;return}var u=a.indexOf(t);if(u===-1)return;o<=u&&(i=o)}if(s=a[i]||e.target,s!==t){Gt(e,"currentTarget",{configurable:!0,get(){return s||n}});var f=$,_=C;ve(null),de(null);try{for(var v,p=[];s!==null;){var d=s.assignedSlot||s.parentNode||s.host||null;try{var h=s["__"+r];if(h!=null&&(!s.disabled||e.target===s))if(tn(h)){var[m,...g]=h;m.apply(s,[e,...g])}else h.call(s,e)}catch(b){v?p.push(b):v=b}if(e.cancelBubble||d===t||d===null)break;s=d}if(v){for(let b of p)queueMicrotask(()=>{throw b});throw v}}finally{e.__root=t,delete e.currentTarget,ve(f),de(_)}}}function Xa(e){var t=document.createElement("template");return t.innerHTML=e.replaceAll("<!>","<!---->"),t.content}function $n(e,t){var n=C;n.nodes_start===null&&(n.nodes_start=e,n.nodes_end=t)}function Y(e,t){var n=(t&da)!==0,r=(t&pa)!==0,a,s=!e.startsWith("<!>");return()=>{a===void 0&&(a=Xa(s?e:"<!>"+e),n||(a=Zt(a)));var i=r||ts?document.importNode(a,!0):a.cloneNode(!0);if(n){var l=Zt(i),o=i.lastChild;$n(l,o)}else $n(i,i);return i}}function $e(){var e=document.createDocumentFragment(),t=document.createComment(""),n=nt();return e.append(t,n),$n(t,n),e}function L(e,t){e!==null&&e.before(t)}let Ln=!0;function Z(e,t){var n=t==null?"":typeof t=="object"?t+"":t;n!==(e.__t??=e.nodeValue)&&(e.__t=n,e.nodeValue=n+"")}function Ja(e,t){return Za(e,t)}const at=new Map;function Za(e,{target:t,anchor:n,props:r={},events:a,context:s,intro:i=!0}){Ia();var l=new Set,o=_=>{for(var v=0;v<_.length;v++){var p=_[v];if(!l.has(p)){l.add(p);var d=Va(p);t.addEventListener(p,kt,{passive:d});var h=at.get(p);h===void 0?(document.addEventListener(p,kt,{passive:d}),at.set(p,1)):at.set(p,h+1)}}};o(Kn(Wa)),Ar.add(o);var u=void 0,f=$a(()=>{var _=n??t.appendChild(nt());return He(()=>{if(s){ke({});var v=j;v.c=s}a&&(r.$$events=a),Ln=i,u=e(_,r)||{},Ln=!0,s&&Ae()}),()=>{for(var v of l){t.removeEventListener(v,kt);var p=at.get(v);--p===0?(document.removeEventListener(v,kt),at.delete(v)):at.set(v,p)}Ar.delete(o),_!==n&&_.parentNode?.removeChild(_)}});return Qa.set(u,f),u}let Qa=new WeakMap;const yn=0,jt=1,wn=2;function ei(e,t,n,r,a){var s=e,i=Ve(),l=j,o=K,u,f,_,v=i?Be(void 0):B(void 0,!1,!1),p=i?Be(void 0):B(void 0,!1,!1),d=!1;function h(g,b){d=!0,b&&(de(m),ve(m),ht(l));try{g===jt&&r&&(f?vn(f):f=He(()=>r(s,v))),g!==yn&&u&&Ue(u,()=>u=null),g!==jt&&f&&Ue(f,()=>f=null),g!==wn&&_&&Ue(_,()=>_=null)}finally{b&&(ht(null),ve(null),de(null),Na())}}var m=Ct(()=>{if(o!==(o=t())){if(Us(o)){var g=o;d=!1,g.then(b=>{g===o&&(je(v,b),h(jt,!0))},b=>{if(g===o)throw je(p,b),h(wn,!0),p.v}),Nt(()=>{d||h(yn,!0)})}else je(v,o),h(jt,!1);return()=>o=K}})}function Se(e,t,n=!1){var r=e,a=null,s=null,i=K,l=n?Rt:0,o=!1;const u=(p,d=!0)=>{o=!0,v(d,p)};var f=null;function _(){f!==null&&(f.lastChild.remove(),r.before(f),f=null);var p=i?a:s,d=i?s:a;p&&vn(p),d&&Ue(d,()=>{i?s=null:a=null})}const v=(p,d)=>{if(i!==(i=p)){var h=cn(),m=r;if(h&&(f=document.createDocumentFragment(),f.append(m=nt())),i?a??=d&&He(()=>d(m)):s??=d&&He(()=>d(m)),h){var g=q,b=i?a:s,w=i?s:a;b&&g.skipped_effects.delete(b),w&&g.skipped_effects.add(w),g.add_callback(_)}else _()}};Ct(()=>{o=!1,t(u),o||v(null,null)},l)}function ti(e,t,n){var r=e,a=K,s,i,l=null,o=Ve()?ma:er;function u(){s&&Ue(s),l!==null&&(l.lastChild.remove(),r.before(l),l=null),s=i}Ct(()=>{if(o(a,a=t())){var f=r,_=cn();_&&(l=document.createDocumentFragment(),l.append(f=nt())),i=He(()=>n(f)),_?q.add_callback(u):u()}})}function ut(e,t){return t}function ni(e,t,n){for(var r=e.items,a=[],s=t.length,i=0;i<s;i++)ar(t[i].e,a,!0);var l=s>0&&a.length===0&&n!==null;if(l){var o=n.parentNode;Ca(o),o.append(n),r.clear(),we(e,t[0].prev,t[s-1].next)}cs(a,()=>{for(var u=0;u<s;u++){var f=t[u];l||(r.delete(f.k),we(e,f.prev,f.next)),Re(f.e,!l)}})}function ct(e,t,n,r,a,s=null){var i=e,l={flags:t,items:new Map,first:null},o=(t&Ur)!==0;if(o){var u=e;i=u.appendChild(nt())}var f=null,_=!1,v=new Map,p=nr(()=>{var g=n();return tn(g)?g:g==null?[]:Kn(g)}),d,h;function m(){ri(h,d,l,v,i,a,t,r,n),s!==null&&(d.length===0?f?vn(f):f=He(()=>s(i)):f!==null&&Ue(f,()=>{f=null}))}Ct(()=>{h??=C,d=c(p);var g=d.length;if(!(_&&g===0)){_=g===0;var b,w,I,R;if(cn()){var y=new Set,E=q;for(w=0;w<g;w+=1){I=d[w],R=r(I,w);var D=l.items.get(R)??v.get(R);D?(t&(an|ln))!==0&&gs(D,I,w,t):(b=bs(null,l,null,null,I,R,w,a,t,n,!0),v.set(R,b)),y.add(R)}for(const[z,k]of l.items)y.has(z)||E.skipped_effects.add(k.e);E.add_callback(m)}else m();c(p)}})}function ri(e,t,n,r,a,s,i,l,o){var u=(i&sa)!==0,f=(i&(an|ln))!==0,_=t.length,v=n.items,p=n.first,d=p,h,m=null,g,b=[],w=[],I,R,y,E;if(u)for(E=0;E<_;E+=1)I=t[E],R=l(I,E),y=v.get(R),y!==void 0&&(y.a?.measure(),(g??=new Set).add(y));for(E=0;E<_;E+=1){if(I=t[E],R=l(I,E),y=v.get(R),y===void 0){var D=r.get(R);if(D!==void 0){r.delete(R),v.set(R,D);var z=m?m.next:d;we(n,m,D),we(n,D,z),xn(D,z,a),m=D}else{var k=d?d.e.nodes_start:a;m=bs(k,n,m,m===null?n.first:m.next,I,R,E,s,i,o)}v.set(R,m),b=[],w=[],d=m.next;continue}if(f&&gs(y,I,E,i),(y.e.f&ge)!==0&&(vn(y.e),u&&(y.a?.unfix(),(g??=new Set).delete(y))),y!==d){if(h!==void 0&&h.has(y)){if(b.length<w.length){var P=w[0],A;m=P.prev;var T=b[0],N=b[b.length-1];for(A=0;A<b.length;A+=1)xn(b[A],P,a);for(A=0;A<w.length;A+=1)h.delete(w[A]);we(n,T.prev,N.next),we(n,m,T),we(n,N,P),d=P,m=N,E-=1,b=[],w=[]}else h.delete(y),xn(y,d,a),we(n,y.prev,y.next),we(n,y,m===null?n.first:m.next),we(n,m,y),m=y;continue}for(b=[],w=[];d!==null&&d.k!==R;)(d.e.f&ge)===0&&(h??=new Set).add(d),w.push(d),d=d.next;if(d===null)continue;y=d}b.push(y),m=y,d=y.next}if(d!==null||h!==void 0){for(var F=h===void 0?[]:Kn(h);d!==null;)(d.e.f&ge)===0&&F.push(d),d=d.next;var J=F.length;if(J>0){var pe=(i&Ur)!==0&&_===0?a:null;if(u){for(E=0;E<J;E+=1)F[E].a?.measure();for(E=0;E<J;E+=1)F[E].a?.fix()}ni(n,F,pe)}}u&&Nt(()=>{if(g!==void 0)for(y of g)y.a?.apply()}),e.first=n.first&&n.first.e,e.last=m&&m.e;for(var Ye of r.values())Re(Ye.e);r.clear()}function gs(e,t,n,r){(r&an)!==0&&je(e.v,t),(r&ln)!==0?je(e.i,n):e.i=n}function bs(e,t,n,r,a,s,i,l,o,u,f){var _=(o&an)!==0,v=(o&aa)===0,p=_?v?B(a,!1,!1):Be(a):a,d=(o&ln)===0?i:Be(i),h={i:d,v:p,k:s,a:null,e:null,prev:n,next:r};try{if(e===null){var m=document.createDocumentFragment();m.append(e=nt())}return h.e=He(()=>l(e,p,d,u),_a),h.e.prev=n&&n.e,h.e.next=r&&r.e,n===null?f||(t.first=h):(n.next=h,n.e.next=h.e),r!==null&&(r.prev=h,r.e.prev=h.e),h}finally{}}function xn(e,t,n){for(var r=e.next?e.next.e.nodes_start:n,a=t?t.e.nodes_start:n,s=e.e.nodes_start;s!==null&&s!==r;){var i=un(s);a.before(s),s=i}}function we(e,t,n){t===null?e.first=n:(t.next=n,t.e.next=n&&n.e),n!==null&&(n.prev=t,n.e.prev=t&&t.e)}function Fn(e,t,n,r,a){var s=t.$$slots?.[n],i=!1;s===!0&&(s=t.children,i=!0),s===void 0||s(e,i?()=>r:r)}function si(e,t,n){var r=e,a,s,i=null,l=null;function o(){s&&(Ue(s),s=null),i&&(i.lastChild.remove(),r.before(i),i=null),s=l,l=null}Ct(()=>{if(a!==(a=t())){var u=cn();if(a){var f=r;u&&(i=document.createDocumentFragment(),i.append(f=nt())),l=He(()=>n(f,a))}u?q.add_callback(o):o()}},Rt)}function ft(e,t,n){sr(()=>{var r=H(()=>t(e,n?.())||{});if(r?.destroy)return()=>r.destroy()})}function ys(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(n=ys(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function ai(){for(var e,t,n=0,r="",a=arguments.length;n<a;n++)(e=arguments[n])&&(t=ys(e))&&(r&&(r+=" "),r+=t);return r}function Sn(e){return typeof e=="object"?ai(e):e??""}function ii(e,t,n){var r=e==null?"":""+e;return t&&(r=r?r+" "+t:t),r===""?null:r}function Wt(e,t,n,r,a,s){var i=e.__className;if(i!==n||i===void 0){var l=ii(n,r);l==null?e.removeAttribute("class"):e.className=l,e.__className=n}return s}const li=Symbol("is custom element"),oi=Symbol("is html");function zr(e,t){var n=ws(e);n.value===(n.value=t??void 0)||e.value===t&&(t!==0||e.nodeName!=="PROGRESS")||(e.value=t??"")}function en(e,t,n,r){var a=ws(e);a[t]!==(a[t]=n)&&(t==="loading"&&(e[Ks]=n),n==null?e.removeAttribute(t):typeof n!="string"&&ui(e).includes(t)?e[t]=n:e.setAttribute(t,n))}function ws(e){return e.__attributes??={[li]:e.nodeName.includes("-"),[oi]:e.namespaceURI===ha}}var Pr=new Map;function ui(e){var t=Pr.get(e.nodeName);if(t)return t;Pr.set(e.nodeName,t=[]);for(var n,r=e,a=Element.prototype;a!==r;){n=Fr(r);for(var s in n)n[s].set&&t.push(s);r=Wn(r)}return t}const ci=()=>performance.now(),ze={tick:e=>requestAnimationFrame(e),now:()=>ci(),tasks:new Set};function xs(){const e=ze.now();ze.tasks.forEach(t=>{t.c(e)||(ze.tasks.delete(t),t.f())}),ze.tasks.size!==0&&ze.tick(xs)}function fi(e){let t;return ze.tasks.size===0&&ze.tick(xs),{promise:new Promise(n=>{ze.tasks.add(t={c:e,f:n})}),abort(){ze.tasks.delete(t)}}}function Ut(e,t){dn(()=>{e.dispatchEvent(new CustomEvent(t))})}function vi(e){if(e==="float")return"cssFloat";if(e==="offset")return"cssOffset";if(e.startsWith("--"))return e;const t=e.split("-");return t.length===1?t[0]:t[0]+t.slice(1).map(n=>n[0].toUpperCase()+n.slice(1)).join("")}function Rr(e){const t={},n=e.split(";");for(const r of n){const[a,s]=r.split(":");if(!a||s===void 0)break;const i=vi(a.trim());t[i]=s.trim()}return t}const di=e=>e;function Nr(e,t,n,r){var a=(e&ca)!==0,s=(e&fa)!==0,i=a&&s,l=(e&va)!==0,o=i?"both":a?"in":"out",u,f=t.inert,_=t.style.overflow,v,p;function d(){return dn(()=>u??=n()(t,r?.()??{},{direction:o}))}var h={is_global:l,in(){if(t.inert=f,!a){p?.abort(),p?.reset?.();return}s||v?.abort(),Ut(t,"introstart"),v=qn(t,d(),p,1,()=>{Ut(t,"introend"),v?.abort(),v=u=void 0,t.style.overflow=_})},out(w){if(!s){w?.(),u=void 0;return}t.inert=!0,Ut(t,"outrostart"),p=qn(t,d(),v,0,()=>{Ut(t,"outroend"),w?.()})},stop:()=>{v?.abort(),p?.abort()}},m=C;if((m.transitions??=[]).push(h),a&&Ln){var g=l;if(!g){for(var b=m.parent;b&&(b.f&Rt)!==0;)for(;(b=b.parent)&&(b.f&bt)===0;);g=!b||(b.f&rn)!==0}g&&sr(()=>{H(()=>h.in())})}}function qn(e,t,n,r,a){var s=r===1;if(lt(t)){var i,l=!1;return Nt(()=>{if(!l){var m=t({direction:s?"in":"out"});i=qn(e,m,n,r,a)}}),{abort:()=>{l=!0,i?.abort()},deactivate:()=>i.deactivate(),reset:()=>i.reset(),t:()=>i.t()}}if(n?.deactivate(),!t?.duration)return a(),{abort:ae,deactivate:ae,reset:ae,t:()=>r};const{delay:o=0,css:u,tick:f,easing:_=di}=t;var v=[];if(s&&n===void 0&&(f&&f(0,1),u)){var p=Rr(u(0,1));v.push(p,p)}var d=()=>1-r,h=e.animate(v,{duration:o,fill:"forwards"});return h.onfinish=()=>{h.cancel();var m=n?.t()??1-r;n?.abort();var g=r-m,b=t.duration*Math.abs(g),w=[];if(b>0){var I=!1;if(u)for(var R=Math.ceil(b/16.666666666666668),y=0;y<=R;y+=1){var E=m+g*_(y/R),D=Rr(u(E,1-E));w.push(D),I||=D.overflow==="hidden"}I&&(e.style.overflow="hidden"),d=()=>{var z=h.currentTime;return m+g*_(z/b)},f&&fi(()=>{if(h.playState!=="running")return!1;var z=d();return f(z,1-z),!0})}h=e.animate(w,{duration:b,fill:"forwards"}),h.onfinish=()=>{d=()=>r,f?.(r,1-r),a()}},{abort:()=>{h&&(h.cancel(),h.effect=null,h.onfinish=ae)},deactivate:()=>{a=ae},reset:()=>{r===0&&f?.(1,0)},t:()=>d()}}function jn(e,t,n=t){var r=Ve(),a=new WeakSet;Ka(e,"input",s=>{var i=s?e.defaultValue:e.value;if(i=En(e)?kn(i):i,n(i),q!==null&&a.add(q),r&&i!==(i=t())){var l=e.selectionStart,o=e.selectionEnd;e.value=i??"",o!==null&&(e.selectionStart=l,e.selectionEnd=Math.min(o,e.value.length))}}),H(t)==null&&e.value&&(n(En(e)?kn(e.value):e.value),q!==null&&a.add(q)),fn(()=>{var s=t();if(e===document.activeElement){var i=Yt??q;if(a.has(i))return}En(e)&&s===kn(e.value)||e.type==="date"&&!s&&!e.value||s!==e.value&&(e.value=s??"")})}function En(e){var t=e.type;return t==="number"||t==="range"}function kn(e){return e===""?null:+e}function Or(e,t){return e===t||e?.[qe]===t}function Ss(e={},t,n,r){return sr(()=>{var a,s;return fn(()=>{a=s,s=[],H(()=>{e!==n(...s)&&(t(e,...s),a&&Or(n(...a),e)&&t(null,...a))})}),()=>{Nt(()=>{s&&Or(n(...s),e)&&t(null,...s)})}}),e}function Ie(e=!1){const t=j,n=t.l.u;if(!n)return;let r=()=>Pt(t.s);if(e){let a=0,s={};const i=Ot(()=>{let l=!1;const o=t.s;for(const u in o)o[u]!==s[u]&&(s[u]=o[u],l=!0);return l&&a++,a});r=()=>c(i)}n.b.length&&Da(()=>{Ir(t,r),At(n.b)}),Mn(()=>{const a=H(()=>n.m.map(Bs));return()=>{for(const s of a)typeof s=="function"&&s()}}),n.a.length&&Mn(()=>{Ir(t,r),At(n.a)})}function Ir(e,t){if(e.l.s)for(const n of e.l.s)c(n);t()}function ir(e,t,n){if(e==null)return t(void 0),n&&n(void 0),ae;const r=H(()=>e.subscribe(t,n));return r.unsubscribe?()=>r.unsubscribe():r}const it=[];function pi(e,t){return{subscribe:re(e,t).subscribe}}function re(e,t=ae){let n=null;const r=new Set;function a(l){if(er(e,l)&&(e=l,n)){const o=!it.length;for(const u of r)u[1](),it.push(u,e);if(o){for(let u=0;u<it.length;u+=2)it[u][0](it[u+1]);it.length=0}}}function s(l){a(l(e))}function i(l,o=ae){const u=[l,o];return r.add(u),r.size===1&&(n=t(a,s)||ae),l(e),()=>{r.delete(u),r.size===0&&n&&(n(),n=null)}}return{set:a,update:s,subscribe:i}}function pn(e,t,n){const r=!Array.isArray(e),a=r?[e]:e;if(!a.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const s=t.length<2;return pi(n,(i,l)=>{let o=!1;const u=[];let f=0,_=ae;const v=()=>{if(f)return;_();const d=t(r?u[0]:u,i,l);s?i(d):_=typeof d=="function"?d:ae},p=a.map((d,h)=>ir(d,m=>{u[h]=m,f&=~(1<<h),o&&v()},()=>{f|=1<<h}));return o=!0,v(),function(){At(p),_(),o=!1}})}function Ee(e){let t;return ir(e,n=>t=n)(),t}let Bt=!1,Un=Symbol();function Ke(e,t,n){const r=n[t]??={store:null,source:B(void 0),unsubscribe:ae};if(r.store!==e&&!(Un in n))if(r.unsubscribe(),r.store=e??null,e==null)r.source.v=void 0,r.unsubscribe=ae;else{var a=!0;r.unsubscribe=ir(e,s=>{a?r.source.v=s:M(r.source,s)}),a=!1}return e&&Un in n?Ee(e):c(r.source)}function hn(){const e={};function t(){as(()=>{for(var n in e)e[n].unsubscribe();Gt(e,Un,{enumerable:!1,value:!0})})}return[e,t]}function hi(e){var t=Bt;try{return Bt=!1,[e(),Bt]}finally{Bt=t}}const _i={get(e,t){if(!e.exclude.includes(t))return c(e.version),t in e.special?e.special[t]():e.props[t]},set(e,t,n){if(!(t in e.special)){var r=C;try{de(e.parent_effect),e.special[t]=ue({get[t](){return e.props[t]}},t,Br)}finally{de(r)}}return e.special[t](n),yr(e.version),!0},getOwnPropertyDescriptor(e,t){if(!e.exclude.includes(t)&&t in e.props)return{enumerable:!0,configurable:!0,value:e.props[t]}},deleteProperty(e,t){return e.exclude.includes(t)||(e.exclude.push(t),yr(e.version)),!0},has(e,t){return e.exclude.includes(t)?!1:t in e.props},ownKeys(e){return Reflect.ownKeys(e.props).filter(t=>!e.exclude.includes(t))}};function mi(e,t){return new Proxy({props:e,exclude:t,special:{},version:Be(0),parent_effect:C},_i)}const gi={get(e,t){let n=e.props.length;for(;n--;){let r=e.props[n];if(lt(r)&&(r=r()),typeof r=="object"&&r!==null&&t in r)return r[t]}},set(e,t,n){let r=e.props.length;for(;r--;){let a=e.props[r];lt(a)&&(a=a());const s=Fe(a,t);if(s&&s.set)return s.set(n),!0}return!1},getOwnPropertyDescriptor(e,t){let n=e.props.length;for(;n--;){let r=e.props[n];if(lt(r)&&(r=r()),typeof r=="object"&&r!==null&&t in r){const a=Fe(r,t);return a&&!a.configurable&&(a.configurable=!0),a}}},has(e,t){if(t===qe||t===jr)return!1;for(let n of e.props)if(lt(n)&&(n=n()),n!=null&&t in n)return!0;return!1},ownKeys(e){const t=[];for(let n of e.props)if(lt(n)&&(n=n()),!!n){for(const r in n)t.includes(r)||t.push(r);for(const r of Object.getOwnPropertySymbols(n))t.includes(r)||t.push(r)}return t}};function bi(...e){return new Proxy({props:e},gi)}function ue(e,t,n,r){var a=!yt||(n&la)!==0,s=(n&oa)!==0,i=(n&ua)!==0,l=r,o=!0,u=()=>(o&&(o=!1,l=i?H(r):r),l),f;if(s){var _=qe in e||jr in e;f=Fe(e,t)?.set??(_&&t in e?w=>e[t]=w:void 0)}var v,p=!1;s?[v,p]=hi(()=>e[t]):v=e[t],v===void 0&&r!==void 0&&(v=u(),f&&(a&&ea(),f(v)));var d;if(a?d=()=>{var w=e[t];return w===void 0?u():(o=!0,w)}:d=()=>{var w=e[t];return w!==void 0&&(l=void 0),w===void 0?l:w},a&&(n&Br)===0)return d;if(f){var h=e.$$legacy;return function(w,I){return arguments.length>0?((!a||!I||h||p)&&f(I?d():w),w):d()}}var m=!1,g=((n&ia)!==0?Ot:nr)(()=>(m=!1,d()));s&&c(g);var b=C;return function(w,I){if(arguments.length>0){const R=I?c(g):a&&s?ot(w):w;return M(g,R),m=!0,l!==void 0&&(l=R),w}return rt&&m||(b.f&tt)!==0?g.v:c(g)}}function _n(e){j===null&&sn(),yt&&j.l!==null?wi(j).m.push(e):Mn(()=>{const t=H(e);if(typeof t=="function")return t})}function lr(e){j===null&&sn(),_n(()=>()=>H(e))}function yi(e,t,{bubbles:n=!1,cancelable:r=!1}={}){return new CustomEvent(e,{detail:t,bubbles:n,cancelable:r})}function Es(){const e=j;return e===null&&sn(),(t,n,r)=>{const a=e.s.$$events?.[t];if(a){const s=tn(a)?a.slice():[a],i=yi(t,n,r);for(const l of s)l.call(e.x,i);return!i.defaultPrevented}return!0}}function wi(e){var t=e.l;return t.u??={a:[],b:[],m:[]}}const xi="5";typeof window<"u"&&((window.__svelte??={}).v??=new Set).add(xi);ba();const Cr={},Bn={},Si={},ks=/^:(.+)/,Mr=4,Ei=3,ki=2,Ai=1,Ti=1,Hn=e=>e.replace(/(^\/+|\/+$)/g,"").split("/"),An=e=>e.replace(/(^\/+|\/+$)/g,""),zi=(e,t)=>{const n=e.default?0:Hn(e.path).reduce((r,a)=>(r+=Mr,a===""?r+=Ti:ks.test(a)?r+=ki:a[0]==="*"?r-=Mr+Ai:r+=Ei,r),0);return{route:e,score:n,index:t}},Pi=e=>e.map(zi).sort((t,n)=>t.score<n.score?1:t.score>n.score?-1:t.index-n.index),Dr=(e,t)=>{let n,r;const[a]=t.split("?"),s=Hn(a),i=s[0]==="",l=Pi(e);for(let o=0,u=l.length;o<u;o++){const f=l[o].route;let _=!1;if(f.default){r={route:f,params:{},uri:t};continue}const v=Hn(f.path),p={},d=Math.max(s.length,v.length);let h=0;for(;h<d;h++){const m=v[h],g=s[h];if(m&&m[0]==="*"){const w=m==="*"?"*":m.slice(1);p[w]=s.slice(h).map(decodeURIComponent).join("/");break}if(typeof g>"u"){_=!0;break}const b=ks.exec(m);if(b&&!i){const w=decodeURIComponent(g);p[b[1]]=w}else if(m!==g){_=!0;break}}if(!_){n={route:f,params:p,uri:"/"+s.slice(0,h).join("/")};break}}return n||r||null},Tn=(e,t)=>`${An(t==="/"?e:`${An(e)}/${An(t)}`)}/`,Ri=e=>!e.defaultPrevented&&e.button===0&&!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey),Ni=e=>{const t=location.host;return e.host===t||e.href.indexOf(`https://${t}`)===0||e.href.indexOf(`http://${t}`)===0},Vn=()=>typeof window<"u"&&"document"in window&&"location"in window;function zn(e,t){const n=mi(t,["children","$$slots","$$events","$$legacy"]);ke(t,!1);const[r,a]=hn(),s=()=>Ke(v,"$activeRoute",r);let i=ue(t,"path",8,""),l=ue(t,"component",12,null),o=B({}),u=B({});const{registerRoute:f,unregisterRoute:_,activeRoute:v}=On(Bn),p={path:i(),default:i()===""};f(p),lr(()=>{_(p)}),Qe(()=>(s(),Pt(n),Vn),()=>{if(s()&&s().route===p){M(o,s().params);const{component:g,path:b,...w}=n;M(u,w),g&&(g.toString().startsWith("class ")?l(g):l(g())),Vn()&&!s().preserveScroll&&window?.scrollTo(0,0)}}),It(),Ie();var d=$e(),h=me(d);{var m=g=>{var b=$e(),w=me(b);{var I=y=>{var E=$e(),D=me(E);ei(D,l,null,(z,k)=>{var P=$e(),A=me(P);si(A,()=>c(k)?.default||c(k),(T,N)=>{N(T,bi(()=>c(o),()=>c(u)))}),L(z,P)}),L(y,E)},R=y=>{var E=$e(),D=me(E);Fn(D,t,"default",{get params(){return c(o)}}),L(y,E)};Se(w,y=>{l()?y(I):y(R,!1)})}L(g,b)};Se(h,g=>{s(),H(()=>s()&&s().route===p)&&g(m)})}L(e,d),Ae(),a()}const Pn=e=>({...e.location,state:e.history.state,key:e.history.state&&e.history.state.key||"initial"}),Oi=e=>{const t=[];let n=Pn(e);return{get location(){return n},listen(r){t.push(r);const a=()=>{n=Pn(e),r({location:n,action:"POP"})};return e.addEventListener("popstate",a),()=>{e.removeEventListener("popstate",a);const s=t.indexOf(r);t.splice(s,1)}},navigate(r,{state:a,replace:s=!1,preserveScroll:i=!1,blurActiveElement:l=!0}={}){a={...a,key:Date.now()+""};try{s?e.history.replaceState(a,"",r):e.history.pushState(a,"",r)}catch{e.location[s?"replace":"assign"](r)}n=Pn(e),t.forEach(o=>o({location:n,action:"PUSH",preserveScroll:i})),l&&document.activeElement.blur()}}},Ii=(e="/")=>{let t=0;const n=[{pathname:e,search:""}],r=[];return{get location(){return n[t]},addEventListener(a,s){},removeEventListener(a,s){},history:{get entries(){return n},get index(){return t},get state(){return r[t]},pushState(a,s,i){const[l,o=""]=i.split("?");t++,n.push({pathname:l,search:o}),r.push(a)},replaceState(a,s,i){const[l,o=""]=i.split("?");n[t]={pathname:l,search:o},r[t]=a}}}},As=Oi(Vn()?window:Ii()),{navigate:Ci}=As;var Mi=Y("<div><!></div>");function Di(e,t){ke(t,!1);const[n,r]=hn(),a=()=>Ke(w,"$base",n),s=()=>Ke(b,"$location",n),i=()=>Ke(h,"$routes",n),l=()=>Ke(m,"$activeRoute",n);let o=ue(t,"basepath",8,"/"),u=ue(t,"url",8,null),f=ue(t,"viewtransition",8,null),_=ue(t,"history",8,As);const v=(A,T,N)=>{const F=f()(N);return typeof F?.fn=="function"?F.fn(A,F):F};gn(Si,_());const p=On(Cr),d=On(Bn),h=re([]),m=re(null);let g=!1;const b=p||re(u()?{pathname:u()}:_().location),w=d?d.routerBase:re({path:o(),uri:o()}),I=pn([w,m],([A,T])=>{if(!T)return A;const{path:N}=A,{route:F,uri:J}=T;return{path:F.default?N:F.path.replace(/\*.*$/,""),uri:J}}),R=A=>{const{path:T}=a();let{path:N}=A;if(A._path=N,A.path=Tn(T,N),typeof window>"u"){if(g)return;const F=Dr([A],s().pathname);F&&(m.set(F),g=!0)}else h.update(F=>[...F,A])},y=A=>{h.update(T=>T.filter(N=>N!==A))};let E=B(!1);p||(_n(()=>_().listen(T=>{M(E,T.preserveScroll||!1),b.set(T.location)})),gn(Cr,b)),gn(Bn,{activeRoute:m,base:w,routerBase:I,registerRoute:R,unregisterRoute:y}),Qe(()=>(a(),Tn),()=>{const{path:A}=a();h.update(T=>T.map(N=>Object.assign(N,{path:Tn(A,N._path)})))}),Qe(()=>(i(),s(),c(E)),()=>{const A=Dr(i(),s().pathname);m.set(A&&{...A,preserveScroll:c(E)})}),It(),Ie();var D=$e(),z=me(D);{var k=A=>{var T=$e(),N=me(T);ti(N,()=>(s(),H(()=>s().pathname)),F=>{var J=Mi(),pe=x(J);Fn(pe,t,"default",{get route(){return l(),H(()=>l()&&l().uri)},get location(){return s()}}),Nr(1,J,()=>v),Nr(2,J,()=>v),L(F,J)}),L(A,T)},P=A=>{var T=$e(),N=me(T);Fn(N,t,"default",{get route(){return l(),H(()=>l()&&l().uri)},get location(){return s()}}),L(A,T)};Se(z,A=>{f()?A(k):A(P,!1)})}L(e,D),Ae(),r()}const vt=e=>{const t=n=>{const r=n.currentTarget;(r.target===""||r.target==="_self")&&Ni(r)&&Ri(n)&&(n.preventDefault(),Ci(r.pathname+r.search,{replace:r.hasAttribute("replace"),preserveScroll:r.hasAttribute("preserveScroll")}))};return e.addEventListener("click",t),{destroy(){e.removeEventListener("click",t)}}};var $i=Y('<header class="header svelte-6hbp7t"><div class="container svelte-6hbp7t"><div class="logo svelte-6hbp7t"><h1 class="svelte-6hbp7t"><a href="/" class="svelte-6hbp7t">算法洞察</a></h1> <span class="tagline svelte-6hbp7t">AlgoInsight - 可视化学习算法</span></div> <nav class="nav svelte-6hbp7t"><a href="/" class="nav-link svelte-6hbp7t">首页</a> <a href="/visualization" class="nav-link svelte-6hbp7t">算法可视化</a> <a href="/benchmark" class="nav-link svelte-6hbp7t">性能评测</a></nav></div></header>');function Li(e){var t=$i(),n=x(t),r=x(n),a=x(r),s=x(a);ft(s,f=>vt?.(f));var i=S(r,2),l=x(i);ft(l,f=>vt?.(f));var o=S(l,2);ft(o,f=>vt?.(f));var u=S(o,2);ft(u,f=>vt?.(f)),L(e,t)}var Fi=Y('<div class="card"><h2>欢迎使用算法洞察平台</h2> <p>可视化学习常见排序与搜索算法，并对不同算法进行性能对比。</p> <div class="actions svelte-xmseaw"><a href="/visualization" class="btn-primary">开始可视化</a> <a href="/benchmark" class="btn-secondary">性能评测</a></div></div>');function qi(e){var t=Fi(),n=S(x(t),4),r=x(n);ft(r,s=>vt?.(s));var a=S(r,2);ft(a,s=>vt?.(s)),L(e,t)}const or=re([]),Yn=re(null),X=re({isPlaying:!1,currentStep:0,totalSteps:0,speed:5,stepMode:!1}),wt=re([]),Dt=re([]),De=re({isRunning:!1,progress:0,selectedAlgorithms:[],selectedDataSizes:[],selectedDataTypes:[],iterations:3}),$r=re([]),gt=re({algorithms:!1,visualization:!1,benchmark:!1}),ur=re(null),ji=pn([wt,X],([e,t])=>e.length===0||t.currentStep>=e.length?null:e[t.currentStep]),Ui=pn(X,e=>e.currentStep<e.totalSteps-1),Bi=pn(X,e=>e.currentStep>0);function Hi(){X.set({isPlaying:!1,currentStep:0,totalSteps:0,speed:5,stepMode:!1}),wt.set([]),Dt.set([])}function Vi(e){wt.set(e),X.update(t=>({...t,totalSteps:e.length,currentStep:0})),e.length>0&&Dt.set([...e[0].data])}function Rn(e){const t=Ee(wt),n=Ee(X),r=Math.max(0,Math.min(e,n.totalSteps-1));X.set({...n,currentStep:r}),t.length>r&&Dt.set([...t[r].data])}function Ts(){const e=Ee(X);if(e.currentStep<e.totalSteps-1){const t=e.currentStep+1,n=Ee(wt);n.length>t&&Dt.set([...n[t].data]),X.set({...e,currentStep:t})}}function Yi(){const e=Ee(X);if(e.currentStep>0){const t=e.currentStep-1,n=Ee(wt);n.length>t&&Dt.set([...n[t].data]),X.set({...e,currentStep:t})}}let _e=null;function Ki(){_e&&(clearInterval(_e),_e=null);const e=Ee(X);X.set({...e,isPlaying:!0}),_e=setInterval(()=>{const r=Ee(X);if(!r.isPlaying){clearInterval(_e),_e=null;return}if(r.currentStep>=r.totalSteps-1){clearInterval(_e),_e=null,X.set({...r,isPlaying:!1});return}Ts()},(()=>{const r=Math.max(1,Math.min(10,Ee(X).speed));return Math.max(50,1e3/r)})())}function Ht(){X.update(e=>({...e,isPlaying:!1})),_e&&(clearInterval(_e),_e=null)}var Wi=Y('<div><div class="algorithm-name svelte-sz1wvd"> </div> <div class="algorithm-complexity svelte-sz1wvd"><div class="complexity-item svelte-sz1wvd"><span class="label svelte-sz1wvd">平均:</span> <span class="value svelte-sz1wvd"> </span></div> <div class="complexity-item svelte-sz1wvd"><span class="label svelte-sz1wvd">空间:</span> <span class="value svelte-sz1wvd"> </span></div></div> <div class="algorithm-description svelte-sz1wvd"> </div></div>'),Gi=Y('<div class="category-group svelte-sz1wvd"><h4 class="category-title svelte-sz1wvd"> </h4> <div class="algorithm-cards svelte-sz1wvd"></div></div>'),Xi=Y('<div class="target-input"><h4 class="svelte-sz1wvd">搜索目标</h4> <input type="number" placeholder="请输入要搜索的数值" class="target-input-field svelte-sz1wvd"/></div>'),Ji=Y('<span class="search-note svelte-sz1wvd">* 搜索算法需要输入目标值</span>'),Zi=Y('<div class="selected-info svelte-sz1wvd"><strong>已选择:</strong> <!></div>'),Qi=Y('<div class="algorithm-selector svelte-sz1wvd"><div class="section svelte-sz1wvd"><h3 class="svelte-sz1wvd">选择算法</h3> <div class="algorithm-grid svelte-sz1wvd"></div></div> <div class="section svelte-sz1wvd"><h3 class="svelte-sz1wvd">输入数据</h3> <div class="data-input-controls svelte-sz1wvd"><div class="preset-buttons"><h4 class="svelte-sz1wvd">预设数据</h4> <div class="button-group svelte-sz1wvd"><button class="preset-btn svelte-sz1wvd">随机数据</button> <button class="preset-btn svelte-sz1wvd">已排序</button> <button class="preset-btn svelte-sz1wvd">逆序数据</button> <button class="preset-btn svelte-sz1wvd">近似排序</button> <button class="preset-btn svelte-sz1wvd">重复数据</button></div></div> <div class="custom-input"><h4 class="svelte-sz1wvd">自定义数据</h4> <div class="input-group"><textarea placeholder="请输入数字，用逗号分隔，例如: 64, 34, 25, 12, 22, 11, 90" rows="3" class="data-textarea svelte-sz1wvd"></textarea> <div class="input-info svelte-sz1wvd"><span> </span> <span> </span></div></div></div> <!></div></div> <div class="section svelte-sz1wvd"><div class="action-buttons svelte-sz1wvd"><button class="start-btn btn-primary svelte-sz1wvd">开始可视化</button> <!></div></div></div>');function el(e,t){ke(t,!1);const n=B(),r=B(),a=Es();let s=B([]),i=B(null),l=B("64, 34, 25, 12, 22, 11, 90"),o=B("");or.subscribe(O=>{M(s,O)}),Yn.subscribe(O=>{M(i,O)});function u(O){Yn.set(O),a("algorithm-selected",O)}function f(){const O=8+Math.floor(Math.random()*7),U=[];for(let ee=0;ee<O;ee++)U.push(Math.floor(Math.random()*100)+1);M(l,U.join(", "))}function _(O){let U=[];switch(O){case"sorted":U=[10,20,30,40,50,60,70,80];break;case"reverse":U=[80,70,60,50,40,30,20,10];break;case"nearly-sorted":U=[10,20,25,30,35,40,60,50];break;case"duplicate":U=[30,10,30,20,30,10,20,30];break;default:f();return}M(l,U.join(", "))}function v(){try{return c(l).split(",").map(O=>O.trim()).filter(O=>O!=="").map(O=>parseInt(O)).filter(O=>!isNaN(O))}catch{return[]}}function p(){if(!c(i)){alert("请选择一个算法");return}const O=v();if(O.length===0){alert("请输入有效的数字数组");return}if(O.length>20){alert("数据量过大，请限制在20个数字以内");return}let U;if(c(i).category==="searching"){if(!c(o).trim()){alert("搜索算法需要输入目标值");return}if(U=parseInt(c(o).trim()),isNaN(U)){alert("请输入有效的目标值");return}}a("start-visualization",{algorithm:c(i),data:O,target:U})}function d(O){const U={};return O.forEach(ee=>{U[ee.category]||(U[ee.category]=[]),U[ee.category].push(ee)}),U}Qe(()=>c(s),()=>{M(n,d(c(s)))}),Qe(()=>c(i),()=>{M(r,c(i)?.category==="searching")}),It(),Ie();var h=Qi(),m=x(h),g=S(x(m),2);ct(g,5,()=>(c(n),H(()=>Object.entries(c(n)))),ut,(O,U,ee,Lt)=>{var Ft=Ta(()=>Vs(c(U),2));let Ce=()=>c(Ft)[0],mn=()=>c(Ft)[1];var fr=Gi(),vr=x(fr),zs=x(vr),Ps=S(vr,2);ct(Ps,5,mn,ut,(Rs,he)=>{var qt=Wi(),dr=x(qt),Ns=x(dr),pr=S(dr,2),hr=x(pr),Os=S(x(hr),2),Is=x(Os),Cs=S(hr,2),Ms=S(x(Cs),2),Ds=x(Ms),$s=S(pr,2),Ls=x($s);oe(()=>{Wt(qt,1,`algorithm-card ${c(i),c(he),H(()=>c(i)?.id===c(he).id?"selected":"")??""}`,"svelte-sz1wvd"),Z(Ns,(c(he),H(()=>c(he).name))),Z(Is,(c(he),H(()=>c(he).timeComplexity.average))),Z(Ds,(c(he),H(()=>c(he).spaceComplexity))),Z(Ls,(c(he),H(()=>c(he).description)))}),W("click",qt,()=>u(c(he))),L(Rs,qt)}),oe(()=>Z(zs,Ce()==="sorting"?"排序算法":Ce()==="searching"?"搜索算法":Ce()==="graph"?"图算法":Ce())),L(O,fr)});var b=S(m,2),w=S(x(b),2),I=x(w),R=S(x(I),2),y=x(R),E=S(y,2),D=S(E,2),z=S(D,2),k=S(z,2),P=S(I,2),A=S(x(P),2),T=x(A),N=S(T,2),F=x(N),J=x(F),pe=S(F,2),Ye=x(pe),xt=S(P,2);{var st=O=>{var U=Xi(),ee=S(x(U),2);jn(ee,()=>c(o),Lt=>M(o,Lt)),L(O,U)};Se(xt,O=>{c(r)&&O(st)})}var V=S(b,2),be=x(V),ye=x(be),St=S(ye,2);{var $t=O=>{var U=Zi(),ee=S(x(U)),Lt=S(ee);{var Ft=Ce=>{var mn=Ji();L(Ce,mn)};Se(Lt,Ce=>{c(r)&&Ce(Ft)})}oe(()=>Z(ee,` ${c(i),H(()=>c(i).name)??""} `)),L(O,U)};Se(St,O=>{c(i)&&O($t)})}oe((O,U,ee)=>{Z(J,`数据数量: ${O??""}`),Z(Ye,`最大值: ${U??""}`),ye.disabled=ee},[()=>H(()=>v().length),()=>H(()=>Math.max(...v())||0),()=>(c(i),H(()=>!c(i)||v().length===0))]),W("click",y,()=>_("random")),W("click",E,()=>_("sorted")),W("click",D,()=>_("reverse")),W("click",z,()=>_("nearly-sorted")),W("click",k,()=>_("duplicate")),jn(T,()=>c(l),O=>M(l,O)),W("click",ye,p),L(e,h),Ae()}var tl=Y('<div class="visualization-canvas svelte-1ym4en8"><canvas class="svelte-1ym4en8"></canvas></div>');function nl(e,t){ke(t,!1);let n=ue(t,"data",24,()=>[]),r=ue(t,"step",8,null),a=ue(t,"width",8,800),s=ue(t,"height",8,400),i=B(),l=B(),o,u=0,f=0;const _=300,v={normal:"#64748b",highlighted:"#f59e0b",compared:"#ef4444",swapped:"#10b981",sorted:"#22c55e",background:"#f8fafc",text:"#1e293b"};_n(()=>{M(l,c(i).getContext("2d")),h()}),lr(()=>{o&&cancelAnimationFrame(o)});function p(){u=0,f=0,d(0)}function d(y){f||(f=y);const E=y-f;u=Math.min(u+E/_,1),h(),u<1&&(f=y,o=requestAnimationFrame(d))}function h(){if(!c(l)||(c(l).clearRect(0,0,a(),s()),G(l,c(l).fillStyle=v.background),c(l).fillRect(0,0,a(),s()),!n()||n().length===0))return;const y=Math.max(...n()),E=(a()-100)/n().length,D=s()-100;if(n().forEach((z,k)=>{const P=z/y*D,A=50+k*E,T=s()-50-P;let N=v.normal;r()&&(r().highlighted&&r().highlighted.includes(k)?N=v.highlighted:r().compared&&r().compared.includes(k)?N=v.compared:r().swapped&&r().swapped.includes(k)?N=v.swapped:(r().action==="finish"||r().action==="complete")&&(N=v.sorted)),G(l,c(l).fillStyle=N),c(l).fillRect(A+2,T,E-4,P),G(l,c(l).strokeStyle="#e2e8f0"),G(l,c(l).lineWidth=1),c(l).strokeRect(A+2,T,E-4,P),G(l,c(l).fillStyle=v.text),G(l,c(l).font="12px Arial"),G(l,c(l).textAlign="center"),c(l).fillText(z.toString(),A+E/2,s()-30),G(l,c(l).fillStyle="#94a3b8"),G(l,c(l).font="10px Arial"),c(l).fillText(k.toString(),A+E/2,s()-15)}),r()){G(l,c(l).fillStyle=v.text),G(l,c(l).font="bold 14px Arial"),G(l,c(l).textAlign="left"),c(l).fillText(`步骤 ${r().step}: ${r().description}`,10,25);const z=m(r().action);z&&(G(l,c(l).fillStyle=g(r().action)),G(l,c(l).font="12px Arial"),c(l).fillText(z,10,45))}b()}function m(y){switch(y){case"compare":return"比较操作";case"swap":return"交换操作";case"move":return"移动操作";case"start":return"开始执行";case"finish":return"执行完成";default:return""}}function g(y){switch(y){case"compare":return v.compared;case"swap":return v.swapped;case"move":return v.highlighted;case"start":return v.normal;case"finish":return v.sorted;default:return v.text}}function b(){const y=[{color:v.normal,text:"正常"},{color:v.highlighted,text:"高亮"},{color:v.compared,text:"比较"},{color:v.swapped,text:"交换"},{color:v.sorted,text:"已排序"}],E=a()-150,D=30;G(l,c(l).font="11px Arial"),G(l,c(l).textAlign="left"),y.forEach((z,k)=>{const P=D+k*20;G(l,c(l).fillStyle=z.color),c(l).fillRect(E,P-8,12,12),G(l,c(l).fillStyle=v.text),c(l).fillText(z.text,E+18,P+2)})}function w(y){const E=c(i).getBoundingClientRect(),D=y.clientX-E.left;if(y.clientY-E.top,n()&&n().length>0){const z=(a()-100)/n().length,k=Math.floor((D-50)/z);if(k>=0&&k<n().length){const P={index:k,value:n()[k]};c(i).dispatchEvent(new CustomEvent("bar-click",{detail:P}))}}}Qe(()=>(c(l),Pt(n()),Pt(r())),()=>{c(l)&&(n()||r())&&p()}),It(),Ie();var I=tl(),R=x(I);Ss(R,y=>M(i,y),()=>c(i)),oe(()=>{en(R,"width",a()),en(R,"height",s())}),W("click",R,w),L(e,I),Ae()}var rl=Y('<div class="playback-controls svelte-zmxps8"><div class="control-section svelte-zmxps8"><h3 class="svelte-zmxps8">播放控制</h3> <div class="button-group svelte-zmxps8"><button class="control-btn svelte-zmxps8" title="重置到开始">⏮️</button> <button class="control-btn svelte-zmxps8" title="上一步">⏪</button> <button class="control-btn play-btn svelte-zmxps8"> </button> <button class="control-btn svelte-zmxps8" title="下一步">⏩</button> <button class="control-btn svelte-zmxps8" title="跳转到结束">⏭️</button></div></div> <div class="progress-section svelte-zmxps8"><h3 class="svelte-zmxps8">进度控制</h3> <div class="progress-info svelte-zmxps8"><span> </span> <span> </span></div> <div class="progress-slider svelte-zmxps8"><input type="range" min="0" class="slider svelte-zmxps8"/></div></div> <div class="speed-section svelte-zmxps8"><h3 class="svelte-zmxps8">播放速度</h3> <div class="speed-info svelte-zmxps8"><span> </span></div> <div class="speed-slider svelte-zmxps8"><input type="range" min="1" max="10" class="slider svelte-zmxps8"/> <div class="speed-labels svelte-zmxps8"><span>慢</span> <span>快</span></div></div></div></div>');function sl(e,t){ke(t,!1);const n=Es();let r=B(!1),a=B(0),s=B(0),i=B(5);X.subscribe(V=>{M(r,V.isPlaying),M(a,V.currentStep),M(s,V.totalSteps),M(i,V.speed)});let l=B(!1),o=B(!1);Ui.subscribe(V=>M(l,V)),Bi.subscribe(V=>M(o,V));function u(){c(r)?Ht():Ki()}function f(){Ht(),Rn(0),n("reset")}function _(){Ht(),Rn(c(s)-1)}function v(V){const be=V.target,ye=parseInt(be.value);Ht(),Rn(ye)}function p(V){const be=V.target,ye=parseInt(be.value);X.update(St=>({...St,speed:ye})),n("speed-change",{speed:ye})}Ie();var d=rl(),h=x(d),m=S(x(h),2),g=x(m),b=S(g,2),w=S(b,2),I=x(w),R=S(w,2),y=S(R,2),E=S(h,2),D=S(x(E),2),z=x(D),k=x(z),P=S(z,2),A=x(P),T=S(D,2),N=x(T),F=S(E,2),J=S(x(F),2),pe=x(J),Ye=x(pe),xt=S(J,2),st=x(xt);oe((V,be)=>{g.disabled=c(s)===0,b.disabled=!c(o),w.disabled=c(s)===0,en(w,"title",c(r)?"暂停":"播放"),Z(I,c(r)?"⏸️":"▶️"),R.disabled=!c(l),y.disabled=c(s)===0,Z(k,`步骤: ${c(a)+1} / ${c(s)??""}`),Z(A,`进度: ${V??""}%`),en(N,"max",be),zr(N,c(a)),N.disabled=c(s)===0,Z(Ye,`速度: ${c(i)??""}x`),zr(st,c(i))},[()=>c(s)>0?Math.round(c(a)/(c(s)-1)*100):0,()=>Math.max(0,c(s)-1)]),W("click",g,f),W("click",b,function(...V){Yi?.apply(this,V)}),W("click",w,u),W("click",R,function(...V){Ts?.apply(this,V)}),W("click",y,_),W("input",N,v),W("input",st,p),L(e,d),Ae()}const al="http://localhost:8080/api";async function Vt(e,t={}){try{const n=await fetch(`${al}${e}`,{headers:{"Content-Type":"application/json",...t.headers},...t}),r=await n.json();if(!n.ok)throw new Error(r.error||"请求失败");return{success:!0,data:r}}catch(n){return console.error("API请求错误:",n),{success:!1,error:n instanceof Error?n.message:"未知错误"}}}class cr{static async getAlgorithms(){return Vt("/algorithms")}static async visualizeAlgorithm(t,n){const r=n!==void 0?`/algorithms/visualize?target=${n}`:"/algorithms/visualize";return Vt(r,{method:"POST",body:JSON.stringify(t)})}static async benchmarkAlgorithms(t){return Vt("/algorithms/benchmark",{method:"POST",body:JSON.stringify(t)})}static async getBenchmarkOptions(){return Vt("/benchmark/options")}}var il=Y("<p>请先在上方选择算法并开始可视化。</p>"),ll=Y('<div class="card"><!></div> <div class="card"><h3>可视化画布</h3> <!></div> <div class="card"><!></div>',1);function ol(e,t){ke(t,!1);const[n,r]=hn(),a=()=>Ke(ji,"$currentStepData",n);Yn.subscribe(h=>h);async function s(h){const{algorithm:m,data:g,target:b}=h.detail;gt.update(R=>({...R,visualization:!0})),Hi();const w={algorithmId:m.id,data:g,options:{speed:5,stepMode:!1}},I=await cr.visualizeAlgorithm(w,b);if(I.success&&I.data){const R=I.data.steps;Vi(R)}else ur.set(I.error||"算法可视化失败");gt.update(R=>({...R,visualization:!1}))}Ie();var i=ll(),l=me(i),o=x(l);el(o,{$$events:{"start-visualization":s}});var u=S(l,2),f=S(x(u),2);{var _=h=>{nl(h,{get data(){return a().data},get step(){return a()},width:1e3,height:420})},v=h=>{var m=il();L(h,m)};Se(f,h=>{a()?h(_):h(v,!1)})}var p=S(u,2),d=x(p);sl(d,{}),L(e,i),Ae(),r()}var ul=Y("<canvas></canvas>");function cl(e,t){ke(t,!1);let n=ue(t,"results",24,()=>[]),r=B(),a;function s(){const o={};for(const v of n())o[v.algorithm]||(o[v.algorithm]=[]),o[v.algorithm].push(v);const u=Array.from(new Set(n().map(v=>`${v.dataSize}`))).sort((v,p)=>Number(v)-Number(p)),f=["#3b82f6","#10b981","#f59e0b","#ef4444","#8b5cf6","#14b8a6"],_=Object.entries(o).map(([v,p],d)=>{const h=new Map(p.map(m=>[String(m.dataSize),parseFloat(String(m.executionTime).replace(/[^0-9.]/g,""))]));return{label:v,data:u.map(m=>h.get(m)??0),borderColor:f[d%f.length],backgroundColor:f[d%f.length]+"55",tension:.25}});return{labels:u,datasets:_}}function i(){if(!c(r))return;const{Chart:o}=window;if(!o)return;const u=s();a&&a.destroy?.(),a=new o(c(r).getContext("2d"),{type:"line",data:u,options:{responsive:!0,scales:{y:{title:{display:!0,text:"执行时间 (ms)"}},x:{title:{display:!0,text:"数据规模"}}},plugins:{legend:{position:"bottom"}}}})}lr(()=>a?.destroy?.()),Qe(()=>Pt(n()),()=>{n(),i()}),It(),Ie();var l=ul();Ss(l,o=>M(r,o),()=>c(r)),L(e,l),Ae()}var fl=Y("<button> </button>"),vl=Y("<button> </button>"),dl=Y("<button> </button>"),pl=Y("<li> </li>"),hl=Y("<ul></ul>"),_l=Y('<div class="card"><h3>性能报告</h3> <p><strong>最快算法:</strong> </p> <!></div>'),ml=Y('<div class="card"><h3>执行时间对比</h3> <!></div> <!>',1),gl=Y('<div class="card"><h3>选择算法与参数</h3> <div class="grid svelte-1m4x4tm"><div><div class="form-label" id="label-algos">算法</div> <div class="chips svelte-1m4x4tm"></div></div> <div><div class="form-label" id="label-sizes">数据规模</div> <div class="chips svelte-1m4x4tm"></div></div> <div><div class="form-label" id="label-types">数据类型</div> <div class="chips svelte-1m4x4tm"></div></div> <div><label class="form-label" for="iterations">迭代次数</label> <input id="iterations" class="form-input" type="number" min="1" max="10"/></div></div> <div style="margin-top:1rem;"><button class="btn-primary">开始测试</button></div></div> <!>',1);function bl(e,t){ke(t,!1);const[n,r]=hn(),a=()=>Ke(De,"$benchmarkState",n);let s=B([]);or.subscribe(z=>M(s,z));let i=B(a());De.subscribe(z=>M(i,z));let l=B([]),o=B(null);$r.subscribe(z=>M(l,z));async function u(){if(c(i).selectedAlgorithms.length===0){alert("请选择至少一个算法");return}if(c(i).selectedDataSizes.length===0){alert("请选择至少一个数据规模");return}gt.update(P=>({...P,benchmark:!0})),De.update(P=>({...P,isRunning:!0,progress:0}));const z={algorithms:c(i).selectedAlgorithms,dataSizes:c(i).selectedDataSizes,dataTypes:c(i).selectedDataTypes.length?c(i).selectedDataTypes:["random"],iterations:c(i).iterations||1},k=await cr.benchmarkAlgorithms(z);k.success&&k.data?($r.set(k.data.results),M(o,k.data.analysis)):ur.set(k.error||"基准测试失败"),De.update(P=>({...P,isRunning:!1,progress:100})),gt.update(P=>({...P,benchmark:!1}))}Ie();var f=gl(),_=me(f),v=S(x(_),2),p=x(v),d=S(x(p),2);ct(d,5,()=>c(s),ut,(z,k)=>{var P=fl(),A=x(P);oe(T=>{Wt(P,1,T,"svelte-1m4x4tm"),Z(A,c(k).name)},[()=>Sn(c(i).selectedAlgorithms.includes(c(k).id)?"chip chip--active":"chip")]),W("click",P,()=>De.update(T=>({...T,selectedAlgorithms:T.selectedAlgorithms.includes(c(k).id)?T.selectedAlgorithms.filter(N=>N!==c(k).id):[...T.selectedAlgorithms,c(k).id]}))),L(z,P)});var h=S(p,2),m=S(x(h),2);ct(m,4,()=>[100,500,1e3,5e3,1e4],ut,(z,k)=>{var P=vl(),A=x(P);oe(T=>{Wt(P,1,T,"svelte-1m4x4tm"),Z(A,k)},[()=>Sn(c(i).selectedDataSizes.includes(k)?"chip chip--active":"chip")]),W("click",P,()=>De.update(T=>({...T,selectedDataSizes:T.selectedDataSizes.includes(k)?T.selectedDataSizes.filter(N=>N!==k):[...T.selectedDataSizes,k]}))),L(z,P)});var g=S(h,2),b=S(x(g),2);ct(b,4,()=>["random","sorted","reverse","nearly-sorted","duplicate"],ut,(z,k)=>{var P=dl(),A=x(P);oe(T=>{Wt(P,1,T,"svelte-1m4x4tm"),Z(A,k)},[()=>Sn(c(i).selectedDataTypes.includes(k)?"chip chip--active":"chip")]),W("click",P,()=>De.update(T=>({...T,selectedDataTypes:T.selectedDataTypes.includes(k)?T.selectedDataTypes.filter(N=>N!==k):[...T.selectedDataTypes,k]}))),L(z,P)});var w=S(g,2),I=S(x(w),2),R=S(v,2),y=x(R),E=S(_,2);{var D=z=>{var k=ml(),P=me(k),A=S(x(P),2);cl(A,{get results(){return c(l)}});var T=S(P,2);{var N=F=>{var J=_l(),pe=S(x(J),2),Ye=S(x(pe)),xt=S(pe,2);{var st=V=>{var be=hl();ct(be,5,()=>c(o).recommendations,ut,(ye,St)=>{var $t=pl(),O=x($t);oe(()=>Z(O,c(St))),L(ye,$t)}),L(V,be)};Se(xt,V=>{c(o).recommendations&&c(o).recommendations.length&&V(st)})}oe(()=>Z(Ye,` ${c(o).summary?.fastestAlgorithm??""}（${c(o).summary?.fastestTime??""}）`)),L(F,J)};Se(T,F=>{c(o)&&F(N)})}L(z,k)};Se(E,z=>{c(l).length&&z(D)})}oe(()=>y.disabled=c(i).isRunning),jn(I,()=>c(i).iterations,z=>G(i,c(i).iterations=z)),W("change",I,z=>{const k=Number(z.target.value)||1;De.update(P=>({...P,iterations:Math.max(1,Math.min(10,k))}))}),W("click",y,u),L(e,f),Ae(),r()}var yl=Y('<div class="app svelte-pmg9v0"><!> <main class="main-content svelte-pmg9v0"><!> <!> <!></main></div>');function wl(e,t){ke(t,!1),_n(async()=>{gt.update(r=>({...r,algorithms:!0}));const n=await cr.getAlgorithms();n.success&&n.data?or.set(n.data.algorithms):ur.set(n.error||"加载算法列表失败"),gt.update(r=>({...r,algorithms:!1}))}),Ie(),Di(e,{children:(n,r)=>{var a=yl(),s=x(a);Li(s);var i=S(s,2),l=x(i);zn(l,{path:"/",get component(){return qi}});var o=S(l,2);zn(o,{path:"/visualization",get component(){return ol}});var u=S(o,2);zn(u,{path:"/benchmark",get component(){return bl}}),L(n,a)},$$slots:{default:!0}}),Ae()}Ja(wl,{target:document.getElementById("app")});
