import "./chunk-AMC5CXVK.js";
import "./chunk-3QXOFQXD.js";
import {
  add_locations,
  attribute_effect,
  await_block,
  check_target,
  component,
  createEventDispatcher,
  derived,
  hmr,
  if_block,
  init,
  key,
  legacy_api,
  legacy_rest_props,
  onDestroy,
  onMount,
  prop,
  setup_stores,
  slot,
  spread_props,
  store_get,
  transition,
  validate_store,
  writable
} from "./chunk-XSPWY35O.js";
import "./chunk-DEX2RCYB.js";
import {
  append,
  comment,
  from_html
} from "./chunk-YSXGXCQZ.js";
import {
  FILENAME,
  HMR,
  add_svelte_meta,
  child,
  deep_read_state,
  event,
  first_child,
  get,
  getContext,
  legacy_pre_effect,
  legacy_pre_effect_reset,
  mutable_source,
  pop,
  push,
  reset,
  set,
  setContext,
  strict_equals,
  untrack
} from "./chunk-DZIGR7F7.js";
import "./chunk-SCP42J6T.js";
import "./chunk-LNQKQDIE.js";
import "./chunk-BCBUCTKR.js";

// node_modules/.pnpm/svelte-routing@2.13.0/node_modules/svelte-routing/src/contexts.js
var LOCATION = {};
var ROUTER = {};
var HISTORY = {};
var useLocation = () => getContext(LOCATION);
var useRouter = () => getContext(ROUTER);
var useHistory = () => getContext(HISTORY);

// node_modules/.pnpm/svelte-routing@2.13.0/node_modules/svelte-routing/src/utils.js
var PARAM = /^:(.+)/;
var SEGMENT_POINTS = 4;
var STATIC_POINTS = 3;
var DYNAMIC_POINTS = 2;
var SPLAT_PENALTY = 1;
var ROOT_POINTS = 1;
var segmentize = (uri) => uri.replace(/(^\/+|\/+$)/g, "").split("/");
var stripSlashes = (string) => string.replace(/(^\/+|\/+$)/g, "");
var rankRoute = (route, index) => {
  const score = route.default ? 0 : segmentize(route.path).reduce((score2, segment) => {
    score2 += SEGMENT_POINTS;
    if (segment === "") {
      score2 += ROOT_POINTS;
    } else if (PARAM.test(segment)) {
      score2 += DYNAMIC_POINTS;
    } else if (segment[0] === "*") {
      score2 -= SEGMENT_POINTS + SPLAT_PENALTY;
    } else {
      score2 += STATIC_POINTS;
    }
    return score2;
  }, 0);
  return { route, score, index };
};
var rankRoutes = (routes) => routes.map(rankRoute).sort(
  (a, b) => a.score < b.score ? 1 : a.score > b.score ? -1 : a.index - b.index
);
var pick = (routes, uri) => {
  let match;
  let default_;
  const [uriPathname] = uri.split("?");
  const uriSegments = segmentize(uriPathname);
  const isRootUri = uriSegments[0] === "";
  const ranked = rankRoutes(routes);
  for (let i = 0, l = ranked.length; i < l; i++) {
    const route = ranked[i].route;
    let missed = false;
    if (route.default) {
      default_ = {
        route,
        params: {},
        uri
      };
      continue;
    }
    const routeSegments = segmentize(route.path);
    const params = {};
    const max = Math.max(uriSegments.length, routeSegments.length);
    let index = 0;
    for (; index < max; index++) {
      const routeSegment = routeSegments[index];
      const uriSegment = uriSegments[index];
      if (routeSegment && routeSegment[0] === "*") {
        const splatName = routeSegment === "*" ? "*" : routeSegment.slice(1);
        params[splatName] = uriSegments.slice(index).map(decodeURIComponent).join("/");
        break;
      }
      if (typeof uriSegment === "undefined") {
        missed = true;
        break;
      }
      const dynamicMatch = PARAM.exec(routeSegment);
      if (dynamicMatch && !isRootUri) {
        const value = decodeURIComponent(uriSegment);
        params[dynamicMatch[1]] = value;
      } else if (routeSegment !== uriSegment) {
        missed = true;
        break;
      }
    }
    if (!missed) {
      match = {
        route,
        params,
        uri: "/" + uriSegments.slice(0, index).join("/")
      };
      break;
    }
  }
  return match || default_ || null;
};
var addQuery = (pathname, query) => pathname + (query ? `?${query}` : "");
var resolve = (to, base) => {
  if (to.startsWith("/")) return to;
  const [toPathname, toQuery] = to.split("?");
  const [basePathname] = base.split("?");
  const toSegments = segmentize(toPathname);
  const baseSegments = segmentize(basePathname);
  if (toSegments[0] === "") return addQuery(basePathname, toQuery);
  if (!toSegments[0].startsWith(".")) {
    const pathname = baseSegments.concat(toSegments).join("/");
    return addQuery((basePathname === "/" ? "" : "/") + pathname, toQuery);
  }
  const allSegments = baseSegments.concat(toSegments);
  const segments = [];
  allSegments.forEach((segment) => {
    if (segment === "..") segments.pop();
    else if (segment !== ".") segments.push(segment);
  });
  return addQuery("/" + segments.join("/"), toQuery);
};
var combinePaths = (basepath, path) => `${stripSlashes(
  path === "/" ? basepath : `${stripSlashes(basepath)}/${stripSlashes(path)}`
)}/`;
var shouldNavigate = (event2) => !event2.defaultPrevented && event2.button === 0 && !(event2.metaKey || event2.altKey || event2.ctrlKey || event2.shiftKey);
var hostMatches = (anchor) => {
  const host = location.host;
  return anchor.host === host || anchor.href.indexOf(`https://${host}`) === 0 || anchor.href.indexOf(`http://${host}`) === 0;
};
var canUseDOM = () => typeof window !== "undefined" && "document" in window && "location" in window;

// node_modules/.pnpm/svelte-routing@2.13.0/node_modules/svelte-routing/src/Link.svelte
Link[FILENAME] = "node_modules/.pnpm/svelte-routing@2.13.0/node_modules/svelte-routing/src/Link.svelte";
var root = add_locations(from_html(`<a><!></a>`), Link[FILENAME], [[42, 0]]);
function Link($$anchor, $$props) {
  check_target(new.target);
  const $$sanitized_props = legacy_rest_props($$props, ["children", "$$slots", "$$events", "$$legacy"]);
  const $$restProps = legacy_rest_props($$sanitized_props, ["to", "replace", "state", "getProps", "preserveScroll"]);
  push($$props, false, Link);
  const [$$stores, $$cleanup] = setup_stores();
  const $base = () => (validate_store(base, "base"), store_get(base, "$base", $$stores));
  const $location = () => (validate_store(location2, "location"), store_get(location2, "$location", $$stores));
  const ariaCurrent = mutable_source();
  let to = prop($$props, "to", 8, "#");
  let replace = prop($$props, "replace", 8, false);
  let state = prop($$props, "state", 24, () => ({}));
  let getProps = prop($$props, "getProps", 8, () => ({}));
  let preserveScroll = prop($$props, "preserveScroll", 8, false);
  const location2 = getContext(LOCATION);
  const { base } = getContext(ROUTER);
  const { navigate: navigate2 } = getContext(HISTORY);
  const dispatch = createEventDispatcher();
  let href = mutable_source(), isPartiallyCurrent = mutable_source(), isCurrent = mutable_source(), props = mutable_source();
  const onClick = (event2) => {
    dispatch("click", event2);
    if (shouldNavigate(event2)) {
      event2.preventDefault();
      const shouldReplace = strict_equals($location().pathname, get(href)) || replace();
      navigate2(get(href), {
        state: state(),
        replace: shouldReplace,
        preserveScroll: preserveScroll()
      });
    }
  };
  legacy_pre_effect(() => (resolve, deep_read_state(to()), $base()), () => {
    set(href, resolve(to(), $base().uri));
  });
  legacy_pre_effect(() => ($location(), get(href)), () => {
    set(isPartiallyCurrent, $location().pathname.startsWith(get(href)));
  });
  legacy_pre_effect(() => (get(href), $location()), () => {
    set(isCurrent, strict_equals(get(href), $location().pathname));
  });
  legacy_pre_effect(() => get(isCurrent), () => {
    set(ariaCurrent, get(isCurrent) ? "page" : void 0);
  });
  legacy_pre_effect(
    () => (deep_read_state(getProps()), $location(), get(href), get(isPartiallyCurrent), get(isCurrent), deep_read_state($$restProps)),
    () => {
      set(props, getProps()({
        location: $location(),
        href: get(href),
        isPartiallyCurrent: get(isPartiallyCurrent),
        isCurrent: get(isCurrent),
        existingProps: $$restProps
      }));
    }
  );
  legacy_pre_effect_reset();
  init();
  var a = root();
  attribute_effect(a, () => ({
    href: get(href),
    "aria-current": get(ariaCurrent),
    ...get(props),
    ...$$restProps
  }));
  var node = child(a);
  slot(
    node,
    $$props,
    "default",
    {
      get active() {
        return !!get(ariaCurrent);
      }
    },
    null
  );
  reset(a);
  event("click", a, onClick);
  append($$anchor, a);
  var $$pop = pop({ ...legacy_api() });
  $$cleanup();
  return $$pop;
}
if (import.meta.hot) {
  Link = hmr(Link, () => Link[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Link[HMR].source;
    set(Link[HMR].source, module.default[HMR].original);
  });
}
var Link_default = Link;

// node_modules/.pnpm/svelte-routing@2.13.0/node_modules/svelte-routing/src/Route.svelte
Route[FILENAME] = "node_modules/.pnpm/svelte-routing@2.13.0/node_modules/svelte-routing/src/Route.svelte";
function Route($$anchor, $$props) {
  check_target(new.target);
  const $$sanitized_props = legacy_rest_props($$props, ["children", "$$slots", "$$events", "$$legacy"]);
  push($$props, false, Route);
  const [$$stores, $$cleanup] = setup_stores();
  const $activeRoute = () => (validate_store(activeRoute, "activeRoute"), store_get(activeRoute, "$activeRoute", $$stores));
  let path = prop($$props, "path", 8, "");
  let component2 = prop($$props, "component", 12, null);
  let routeParams = mutable_source({});
  let routeProps = mutable_source({});
  const { registerRoute, unregisterRoute, activeRoute } = getContext(ROUTER);
  const route = {
    path: path(),
    // If no path prop is given, this Route will act as the default Route
    // that is rendered if no other Route in the Router is a match.
    default: strict_equals(path(), "")
  };
  registerRoute(route);
  onDestroy(() => {
    unregisterRoute(route);
  });
  legacy_pre_effect(
    () => ($activeRoute(), deep_read_state($$sanitized_props), canUseDOM),
    () => {
      if ($activeRoute() && strict_equals($activeRoute().route, route)) {
        set(routeParams, $activeRoute().params);
        const { component: c, path: path2, ...rest } = $$sanitized_props;
        set(routeProps, rest);
        if (c) {
          if (c.toString().startsWith("class ")) component2(c);
          else component2(c());
        }
        canUseDOM() && !$activeRoute().preserveScroll && window?.scrollTo(0, 0);
      }
    }
  );
  legacy_pre_effect_reset();
  init();
  var fragment = comment();
  var node = first_child(fragment);
  {
    var consequent_1 = ($$anchor2) => {
      var fragment_1 = comment();
      var node_1 = first_child(fragment_1);
      {
        var consequent = ($$anchor3) => {
          var fragment_2 = comment();
          var node_2 = first_child(fragment_2);
          add_svelte_meta(
            () => await_block(node_2, component2, null, ($$anchor4, resolvedComponent) => {
              var fragment_3 = comment();
              var node_3 = first_child(fragment_3);
              add_svelte_meta(
                () => component(node_3, () => get(resolvedComponent)?.default || get(resolvedComponent), ($$anchor5, $$component) => {
                  $$component($$anchor5, spread_props(() => get(routeParams), () => get(routeProps)));
                }),
                "component",
                Route,
                45,
                12,
                { componentTag: "svelte:component" }
              );
              append($$anchor4, fragment_3);
            }),
            "await",
            Route,
            44,
            8
          );
          append($$anchor3, fragment_2);
        };
        var alternate = ($$anchor3) => {
          var fragment_4 = comment();
          var node_4 = first_child(fragment_4);
          slot(
            node_4,
            $$props,
            "default",
            {
              get params() {
                return get(routeParams);
              }
            },
            null
          );
          append($$anchor3, fragment_4);
        };
        add_svelte_meta(
          () => if_block(node_1, ($$render) => {
            if (component2()) $$render(consequent);
            else $$render(alternate, false);
          }),
          "if",
          Route,
          43,
          4
        );
      }
      append($$anchor2, fragment_1);
    };
    add_svelte_meta(
      () => if_block(node, ($$render) => {
        if ($activeRoute(), untrack(() => $activeRoute() && strict_equals($activeRoute().route, route))) $$render(consequent_1);
      }),
      "if",
      Route,
      42,
      0
    );
  }
  append($$anchor, fragment);
  var $$pop = pop({ ...legacy_api() });
  $$cleanup();
  return $$pop;
}
if (import.meta.hot) {
  Route = hmr(Route, () => Route[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Route[HMR].source;
    set(Route[HMR].source, module.default[HMR].original);
  });
}
var Route_default = Route;

// node_modules/.pnpm/svelte-routing@2.13.0/node_modules/svelte-routing/src/history.js
var getLocation = (source) => {
  return {
    ...source.location,
    state: source.history.state,
    key: source.history.state && source.history.state.key || "initial"
  };
};
var createHistory = (source) => {
  const listeners = [];
  let location2 = getLocation(source);
  return {
    get location() {
      return location2;
    },
    listen(listener) {
      listeners.push(listener);
      const popstateListener = () => {
        location2 = getLocation(source);
        listener({ location: location2, action: "POP" });
      };
      source.addEventListener("popstate", popstateListener);
      return () => {
        source.removeEventListener("popstate", popstateListener);
        const index = listeners.indexOf(listener);
        listeners.splice(index, 1);
      };
    },
    navigate(to, { state, replace = false, preserveScroll = false, blurActiveElement = true } = {}) {
      state = { ...state, key: Date.now() + "" };
      try {
        if (replace) source.history.replaceState(state, "", to);
        else source.history.pushState(state, "", to);
      } catch (e) {
        source.location[replace ? "replace" : "assign"](to);
      }
      location2 = getLocation(source);
      listeners.forEach(
        (listener) => listener({ location: location2, action: "PUSH", preserveScroll })
      );
      if (blurActiveElement) document.activeElement.blur();
    }
  };
};
var createMemorySource = (initialPathname = "/") => {
  let index = 0;
  const stack = [{ pathname: initialPathname, search: "" }];
  const states = [];
  return {
    get location() {
      return stack[index];
    },
    addEventListener(name, fn) {
    },
    removeEventListener(name, fn) {
    },
    history: {
      get entries() {
        return stack;
      },
      get index() {
        return index;
      },
      get state() {
        return states[index];
      },
      pushState(state, _, uri) {
        const [pathname, search = ""] = uri.split("?");
        index++;
        stack.push({ pathname, search });
        states.push(state);
      },
      replaceState(state, _, uri) {
        const [pathname, search = ""] = uri.split("?");
        stack[index] = { pathname, search };
        states[index] = state;
      }
    }
  };
};
var globalHistory = createHistory(
  canUseDOM() ? window : createMemorySource()
);
var { navigate } = globalHistory;

// node_modules/.pnpm/svelte-routing@2.13.0/node_modules/svelte-routing/src/Router.svelte
Router[FILENAME] = "node_modules/.pnpm/svelte-routing@2.13.0/node_modules/svelte-routing/src/Router.svelte";
var root_2 = add_locations(from_html(`<div><!></div>`), Router[FILENAME], [[136, 8]]);
function Router($$anchor, $$props) {
  check_target(new.target);
  push($$props, false, Router);
  const [$$stores, $$cleanup] = setup_stores();
  const $base = () => (validate_store(base, "base"), store_get(base, "$base", $$stores));
  const $location = () => (validate_store(location2, "location"), store_get(location2, "$location", $$stores));
  const $routes = () => (validate_store(routes, "routes"), store_get(routes, "$routes", $$stores));
  const $activeRoute = () => (validate_store(activeRoute, "activeRoute"), store_get(activeRoute, "$activeRoute", $$stores));
  let basepath = prop($$props, "basepath", 8, "/");
  let url = prop($$props, "url", 8, null);
  let viewtransition = prop($$props, "viewtransition", 8, null);
  let history = prop($$props, "history", 8, globalHistory);
  const viewtransitionFn = (node, _, direction) => {
    const vt = viewtransition()(direction);
    if (strict_equals(typeof vt?.fn, "function")) return vt.fn(node, vt);
    else return vt;
  };
  setContext(HISTORY, history());
  const locationContext = getContext(LOCATION);
  const routerContext = getContext(ROUTER);
  const routes = writable([]);
  const activeRoute = writable(null);
  let hasActiveRoute = false;
  const location2 = locationContext || writable(url() ? { pathname: url() } : history().location);
  const base = routerContext ? routerContext.routerBase : writable({ path: basepath(), uri: basepath() });
  const routerBase = derived([base, activeRoute], ([base2, activeRoute2]) => {
    if (!activeRoute2) return base2;
    const { path: basepath2 } = base2;
    const { route, uri } = activeRoute2;
    const path = route.default ? basepath2 : route.path.replace(/\*.*$/, "");
    return { path, uri };
  });
  const registerRoute = (route) => {
    const { path: basepath2 } = $base();
    let { path } = route;
    route._path = path;
    route.path = combinePaths(basepath2, path);
    if (strict_equals(typeof window, "undefined")) {
      if (hasActiveRoute) return;
      const matchingRoute = pick([route], $location().pathname);
      if (matchingRoute) {
        activeRoute.set(matchingRoute);
        hasActiveRoute = true;
      }
    } else {
      routes.update((rs) => [...rs, route]);
    }
  };
  const unregisterRoute = (route) => {
    routes.update((rs) => rs.filter((r) => strict_equals(r, route, false)));
  };
  let preserveScroll = mutable_source(false);
  if (!locationContext) {
    onMount(() => {
      const unlisten = history().listen((event2) => {
        set(preserveScroll, event2.preserveScroll || false);
        location2.set(event2.location);
      });
      return unlisten;
    });
    setContext(LOCATION, location2);
  }
  setContext(ROUTER, {
    activeRoute,
    base,
    routerBase,
    registerRoute,
    unregisterRoute
  });
  legacy_pre_effect(() => ($base(), combinePaths), () => {
    const { path: basepath2 } = $base();
    routes.update((rs) => rs.map((r) => Object.assign(r, { path: combinePaths(basepath2, r._path) })));
  });
  legacy_pre_effect(() => (pick, $routes(), $location(), get(preserveScroll)), () => {
    const bestMatch = pick($routes(), $location().pathname);
    activeRoute.set(bestMatch ? { ...bestMatch, preserveScroll: get(preserveScroll) } : bestMatch);
  });
  legacy_pre_effect_reset();
  init();
  var fragment = comment();
  var node_1 = first_child(fragment);
  {
    var consequent = ($$anchor2) => {
      var fragment_1 = comment();
      var node_2 = first_child(fragment_1);
      add_svelte_meta(
        () => key(node_2, () => ($location(), untrack(() => $location().pathname)), ($$anchor3) => {
          var div = root_2();
          var node_3 = child(div);
          slot(
            node_3,
            $$props,
            "default",
            {
              get route() {
                return $activeRoute(), untrack(() => $activeRoute() && $activeRoute().uri);
              },
              get location() {
                return $location();
              }
            },
            null
          );
          reset(div);
          transition(1, div, () => viewtransitionFn);
          transition(2, div, () => viewtransitionFn);
          append($$anchor3, div);
        }),
        "key",
        Router,
        135,
        4
      );
      append($$anchor2, fragment_1);
    };
    var alternate = ($$anchor2) => {
      var fragment_2 = comment();
      var node_4 = first_child(fragment_2);
      slot(
        node_4,
        $$props,
        "default",
        {
          get route() {
            return $activeRoute(), untrack(() => $activeRoute() && $activeRoute().uri);
          },
          get location() {
            return $location();
          }
        },
        null
      );
      append($$anchor2, fragment_2);
    };
    add_svelte_meta(
      () => if_block(node_1, ($$render) => {
        if (viewtransition()) $$render(consequent);
        else $$render(alternate, false);
      }),
      "if",
      Router,
      134,
      0
    );
  }
  append($$anchor, fragment);
  var $$pop = pop({ ...legacy_api() });
  $$cleanup();
  return $$pop;
}
if (import.meta.hot) {
  Router = hmr(Router, () => Router[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Router[HMR].source;
    set(Router[HMR].source, module.default[HMR].original);
  });
}
var Router_default = Router;

// node_modules/.pnpm/svelte-routing@2.13.0/node_modules/svelte-routing/src/actions.js
var link = (node) => {
  const onClick = (event2) => {
    const anchor = event2.currentTarget;
    if ((anchor.target === "" || anchor.target === "_self") && hostMatches(anchor) && shouldNavigate(event2)) {
      event2.preventDefault();
      navigate(anchor.pathname + anchor.search, {
        replace: anchor.hasAttribute("replace"),
        preserveScroll: anchor.hasAttribute("preserveScroll")
      });
    }
  };
  node.addEventListener("click", onClick);
  return {
    destroy() {
      node.removeEventListener("click", onClick);
    }
  };
};
var links = (node) => {
  const findClosest = (tagName, el) => {
    while (el && el.tagName !== tagName) el = el.parentNode;
    return el;
  };
  const onClick = (event2) => {
    const anchor = findClosest("A", event2.target);
    if (anchor && (anchor.target === "" || anchor.target === "_self") && hostMatches(anchor) && shouldNavigate(event2) && !anchor.hasAttribute("noroute")) {
      event2.preventDefault();
      navigate(anchor.pathname + anchor.search, {
        replace: anchor.hasAttribute("replace"),
        preserveScroll: anchor.hasAttribute("preserveScroll")
      });
    }
  };
  node.addEventListener("click", onClick);
  return {
    destroy() {
      node.removeEventListener("click", onClick);
    }
  };
};
export {
  HISTORY,
  LOCATION,
  Link_default as Link,
  ROUTER,
  Route_default as Route,
  Router_default as Router,
  link,
  links,
  navigate,
  useHistory,
  useLocation,
  useRouter
};
//# sourceMappingURL=svelte-routing.js.map
